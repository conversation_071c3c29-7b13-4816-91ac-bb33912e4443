import { chunkArray } from '../../../utils/array'

describe('Array helpers', () => {
  describe('chunkArray()', () => {
    it('splits an array into equal-sized chunks when length is divisible', () => {
      const data = [1, 2, 3, 4, 5, 6]
      const result = chunkArray(data, 2)
      expect(result).toEqual([
        [1, 2],
        [3, 4],
        [5, 6],
      ])
    })

    it('creates a smaller “remainder” chunk for leftover elements', () => {
      const data = [1, 2, 3, 4, 5]
      const result = chunkArray(data, 2)
      expect(result).toEqual([[1, 2], [3, 4], [5]])
    })

    it('returns a single chunk when batchSize > array length', () => {
      const data = ['a', 'b', 'c']
      const result = chunkArray(data, 10)
      expect(result).toEqual([['a', 'b', 'c']])
    })

    it('returns an empty list for an empty input array', () => {
      const result = chunkArray([], 5)
      expect(result).toEqual([])
    })

    it('does not mutate the source array', () => {
      const data = [1, 2, 3]
      const copy = [...data]
      chunkArray(data, 2)
      expect(data).toEqual(copy)
    })
  })
})
