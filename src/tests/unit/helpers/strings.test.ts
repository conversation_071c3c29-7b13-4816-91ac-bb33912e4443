import { canonicalize, createMD5Hash } from '../../../utils/string'

describe('String helpers', () => {
  describe('canonicalize()', () => {
    it('strips leading / trailing whitespace', () => {
      expect(canonicalize('   hello   ')).toBe('hello')
    })

    it('lower‑cases the string', () => {
      expect(canonicalize('MiXeDCaSe')).toBe('mixedcase')
    })

    it('removes accents / diacritics (NFD + \\p{Diacritic})', () => {
      expect(canonicalize('ÁéÍõÜ')).toBe('aeiou')
      expect(canonicalize('¡Núñez!')).toBe('nunez')
    })

    it('keeps only ASCII letters and digits', () => {
      expect(canonicalize('foo‑BAR_123!')).toBe('foobar123')
    })

    it('returns an empty string when given only ignored characters', () => {
      expect(canonicalize('   —‑‑   ')).toBe('')
    })
  })

  describe('createMD5Hash()', () => {
    it('returns the correct MD5 for a known input', () => {
      // pre‑computed with `echo -n hello | md5`
      expect(createMD5Hash('hello')).toBe('5d41402abc4b2a76b9719d911017c592')
    })

    it('produces different hashes for different inputs', () => {
      const a = createMD5Hash('apple')
      const b = createMD5Hash('orange')
      expect(a).not.toBe(b)
    })

    it('produces same hashes for same inputs', () => {
      const a = createMD5Hash('apple-123')
      const b = createMD5Hash('apple-123')
      expect(a).toBe(b)
    })
  })
})
