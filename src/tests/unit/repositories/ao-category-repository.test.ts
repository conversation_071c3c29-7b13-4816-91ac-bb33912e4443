import dayjs from 'dayjs'

// Mocks
jest.mock('../../../models/schemas/auto-onboarding/AOCategory', () => {
  const batchPutMock = jest.fn()
  return {
    __esModule: true,
    AOCategoryDBModel: { batchPut: batchPutMock },
  }
})

jest.mock('../../../common/db/dynamoose/utils', () => {
  const generateDBKeyV2Mock = jest.fn((parts: any[]) => (parts[0].companyId ? 'pk' : 'sk'))
  return { __esModule: true, generateDBKeyV2: generateDBKeyV2Mock }
})

jest.mock('../../../models/common/enums', () => ({
  EDomain: { CATEGORY: 'categories' },
}))

// Imports
import { AOCategoryRepository } from '../../../repositories/auto-onboarding/AOCategoryRepository'
import { EDomain } from '../../../models/common/enums'
import * as arrayUtils from '../../../utils/array'

// Import mocks
const { generateDBKeyV2 } = jest.requireMock('../../../common/db/dynamoose/utils')
const { AOCategoryDBModel } = jest.requireMock('../../../models/schemas/auto-onboarding/AOCategory')
const batchPutMock = AOCategoryDBModel.batchPut as jest.Mock
const chunkArraySpy = jest.spyOn(arrayUtils, 'chunkArray')

// Tests
describe('AOCategoryRepository', () => {
  describe('bulkCreate()', () => {
    const companyId = '12345'
    const repository = new AOCategoryRepository(companyId)

    beforeEach(() => {
      jest.clearAllMocks()
    })

    it('maps contracts → entities and calls batchPut()', async () => {
      const contracts = [
        {
          key: 'foo',
          name: 'Foo',
          isActive: true,
          unitType: 'gram',
          overrides: [],
          createdAt: dayjs('2025-04-21T12:00:00Z'),
          updatedAt: dayjs('2025-04-21T12:00:00Z'),
          companyId: '12345',
          domain: EDomain.CATEGORY,
        },
        {
          key: 'bar',
          name: 'Bar',
          isActive: false,
          unitType: 'unit',
          overrides: [],
          createdAt: dayjs('2025-04-22T12:00:00Z'),
          updatedAt: dayjs('2025-04-22T12:00:00Z'),
          companyId: '12345',
          domain: EDomain.CATEGORY,
        },
      ] as any

      batchPutMock.mockResolvedValueOnce(undefined)

      const result = await repository.bulkCreate(contracts)

      expect(result).toBe(undefined)

      expect(batchPutMock).toHaveBeenCalledTimes(1)

      const [[entities]] = batchPutMock.mock.calls
      expect(entities).toHaveLength(2)

      expect(entities[0]).toEqual({
        ...contracts[0],
        companyId,
        domain: EDomain.CATEGORY,
        pk: 'pk',
        sk: 'sk',
        createdAt: '2025-04-21T12:00:00.000Z',
        updatedAt: '2025-04-21T12:00:00.000Z',
      })
      expect(entities[1]).toEqual({
        ...contracts[1],
        companyId,
        domain: EDomain.CATEGORY,
        pk: 'pk',
        sk: 'sk',
        createdAt: '2025-04-22T12:00:00.000Z',
        updatedAt: '2025-04-22T12:00:00.000Z',
      })

      expect(generateDBKeyV2).toHaveBeenCalledTimes(4)

      expect(generateDBKeyV2).toHaveBeenNthCalledWith(1, [{ companyId }])
      expect(generateDBKeyV2).toHaveBeenNthCalledWith(2, [
        { domain: EDomain.CATEGORY },
        { uniqueRecord: undefined },
        { key: 'foo' },
      ])

      expect(generateDBKeyV2).toHaveBeenNthCalledWith(3, [{ companyId }])
      expect(generateDBKeyV2).toHaveBeenNthCalledWith(4, [
        { domain: EDomain.CATEGORY },
        { uniqueRecord: undefined },
        { key: 'bar' },
      ])
    })

    it('splits into 25-item batches and calls batchPut per chunk', async () => {
      const contracts = Array.from({ length: 30 }, (_v, i) => ({
        key: `cat-${i}`,
        name: `Category ${i}`,
        isActive: true,
        unitType: 'gram',
        overrides: [],
        createdAt: dayjs('2025-04-21T10:00:00Z'),
        updatedAt: dayjs('2025-04-21T10:00:00Z'),
      })) as any

      batchPutMock.mockResolvedValue(undefined)

      await repository.bulkCreate(contracts)

      expect(batchPutMock).toHaveBeenCalledTimes(2)
      expect(batchPutMock.mock.calls[0][0]).toHaveLength(25)
      expect(batchPutMock.mock.calls[1][0]).toHaveLength(5)

      expect(chunkArraySpy).toHaveBeenCalledWith(expect.any(Array), 25)
    })
  })
})
