import { generateDBKeyV2 } from '../../../common/db/dynamoose/utils'

describe('Repositories utils', () => {
  describe('generateDBKeyV2()', () => {
    it('handles a single key-object with one property', () => {
      const pk = generateDBKeyV2<{ companyId: string }>([{ companyId: '12345' }])
      expect(pk).toBe('companyId#12345')
    })

    it('omits “#value” when the value is undefined or null', () => {
      const key = generateDBKeyV2<{ uniqueRecord: undefined }>([{ uniqueRecord: undefined }])
      expect(key).toBe('uniqueRecord')
    })

    it('joins several single‑prop key‑objects with “#” between them', () => {
      const dbKey = generateDBKeyV2<{
        companyId: string
        domain: string
        uniqueRecord: undefined
        key: string
      }>([
        { companyId: '123' },
        { domain: 'categories' },
        { uniqueRecord: undefined },
        { key: 'bar' },
      ])

      expect(dbKey).toBe('companyId#123#domain#categories#uniqueRecord#key#bar')
    })

    it('stringifies numeric values', () => {
      const key = generateDBKeyV2<{ id: number }>([{ id: 42 }])
      expect(key).toBe('id#42')
    })
  })
})
