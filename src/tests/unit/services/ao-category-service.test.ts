import dayjs from 'dayjs'
import { AOCategoryService } from '../../../services/auto-onboarding/AOCategoryService'

// Mocks

const FROZEN_DATE = new Date('2025-04-21T12:00:00Z')
jest.useFakeTimers().setSystemTime(FROZEN_DATE)

const uuidSequence = ['uuid-1', 'uuid-2', 'uuid-3']
jest.mock('uuid', () => ({
  v4: jest.fn(() => uuidSequence.shift()),
}))

jest.mock('../../../models/common/enums', () => ({
  EDomain: { CATEGORY: 'CATEGORY' },
}))

const mockBulkCreate = jest.fn()
jest.mock('../../../repositories/auto-onboarding/AOCategoryRepository', () => ({
  AOCategoryRepository: jest.fn().mockImplementation(() => ({
    bulkCreate: mockBulkCreate,
  })),
}))

// Tests

describe('AOCategoryService', () => {
  describe('bulkCreate()', () => {
    const companyId = '12345'
    const service = new AOCategoryService(companyId)

    const baseInput = [
      {
        key: 'A',
        name: 'Flowers',
        isActive: true,
        unitType: 'gram',
        overrides: [{ shopId: '1', lowInventoryThreshold: 5 }],
      },
      {
        key: 'B',
        name: 'Edibles',
        isActive: false,
        unitType: 'unit',
        overrides: [],
      },
    ]

    it('enriches each contract with company, domain, timestamps and uuid, then delegates to repository', async () => {
      const repositoryResult = ['ok'] as unknown as any
      mockBulkCreate.mockResolvedValueOnce(repositoryResult)

      const returned = await service.bulkCreate(baseInput as any)

      expect(mockBulkCreate).toHaveBeenCalledTimes(1)
      const [[arg]] = mockBulkCreate.mock.calls
      expect(arg).toHaveLength(2)

      for (let i = 0; i < arg.length; i++) {
        const original = baseInput[i]
        const transformed = arg[i]

        expect(transformed).toMatchObject({
          ...original,
          companyId,
          domain: 'CATEGORY',
          id: `uuid-${i + 1}`,
        })

        expect(dayjs.isDayjs(transformed.createdAt)).toBe(true)
        expect(dayjs.isDayjs(transformed.updatedAt)).toBe(true)
        expect(+transformed.createdAt).toBe(+FROZEN_DATE)
        expect(+transformed.updatedAt).toBe(+FROZEN_DATE)
      }

      expect(returned).toBe(repositoryResult)
    })
  })
})
