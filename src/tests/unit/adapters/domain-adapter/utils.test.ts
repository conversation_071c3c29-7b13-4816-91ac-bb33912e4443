import {
  hashCanonicalStr,
  mergeRecords,
  mostFrequent,
  groupedRecordsByKey,
  unifyRecordGroup,
} from '../../../../adapters/utils'

describe('DomainAdapter utils', () => {
  describe('hashCanonicalStr()', () => {
    it('hashes a canonicalised string (accent, case & punctuation agnostic)', () => {
      const a = hashCanonicalStr('  HÉLLO‑World! ')
      const b = hashCanonicalStr('helloWORLD')

      expect(a).toBe(b)
      expect(a).toBe('fc5e038d38a57032085441e7fe7010b0')
    })

    it('produces different hashes for different canonical values', () => {
      expect(hashCanonicalStr('foo')).not.toBe(hashCanonicalStr('bar'))
    })
  })

  describe('mergeRecords()', () => {
    const defaults = { a: 1, b: 2 }
    const overrides = { a: null, b: 3, c: 4 }

    it('keeps non-null / non-undefined defaults and falls back to overrides otherwise', () => {
      const merged = mergeRecords(defaults, overrides)
      expect(merged).toEqual({ a: 1, b: 2, c: 4 })
    })

    it('does not mutate the original inputs', () => {
      const defaultsClone = { ...defaults }
      const overridesClone = { ...overrides }
      mergeRecords(defaults, overrides)
      expect(defaults).toEqual(defaultsClone)
      expect(overrides).toEqual(overridesClone)
    })
  })

  describe('mostFrequent()', () => {
    it('returns the element with the highest frequency', () => {
      expect(mostFrequent([1, 2, 2, 3])).toBe(2)
    })

    it('prefers the first element that reaches the highest count on ties', () => {
      expect(mostFrequent(['a', 'b', 'a', 'b'])).toBe('a')
    })

    it('returns undefined when undefined elements are more frequent', () => {
      expect(mostFrequent([1, undefined, undefined, null, 'str'])).toBeUndefined()
    })

    it('returns null when null elements are more frequent', () => {
      expect(mostFrequent([1, null, null, undefined, 'str'])).toBeNull()
    })
  })

  describe('groupedRecordsByKey()', () => {
    const records = [
      { id: 1, name: 'foo' },
      { id: 2, name: 'bar' },
      { id: 3, name: 'foo' },
    ]

    it('groups records by the supplied key function', () => {
      const groups = groupedRecordsByKey(records, (r) => r.name)
      expect(groups.size).toBe(2)
      expect(groups.get('foo')?.length).toBe(2)
      expect(groups.get('bar')?.length).toBe(1)
    })
  })

  describe('unifyRecordGroup()', () => {
    interface Category {
      shopId: string
      name: string
      unitType: string
      lowInventoryThreshold: number
    }

    const recordGroup: Category[] = [
      { shopId: '1', name: 'oranges', unitType: 'unit', lowInventoryThreshold: 10 },
      { shopId: '2', name: 'oranges', unitType: 'unit', lowInventoryThreshold: 5 },
      { shopId: '3', name: 'apples', unitType: 'gram', lowInventoryThreshold: 10 },
      { shopId: '4', name: 'apples', unitType: 'gram', lowInventoryThreshold: 10 },
      { shopId: '5', name: 'apples', unitType: 'gram', lowInventoryThreshold: 8 },
      { shopId: '6', name: 'bananas', unitType: 'gram', lowInventoryThreshold: 10 },
    ]

    it('creates a unified record with most-frequent base values and per-shop overrides', () => {
      const unified = unifyRecordGroup<Category, keyof Category>('dedupKey', recordGroup, {
        getId: (record) => `${record.shopId}-${record.name}`,
        fields: ['unitType', 'name', 'unitType', 'lowInventoryThreshold'],
        overrideKey: 'shopId',
        overrideFields: ['lowInventoryThreshold'],
      })

      // dedupKey
      expect(unified.key).toBe('dedupKey')

      // base values
      expect(unified.name).toBe('apples')
      expect(unified.lowInventoryThreshold).toBe(10)

      // overrides array
      expect(unified.overrides).toHaveLength(2)
      expect(unified.overrides).toContainEqual({ shopId: '2', lowInventoryThreshold: 5 })
      expect(unified.overrides).toContainEqual({ shopId: '5', lowInventoryThreshold: 8 })

      // specificRecordsIds array
      expect(unified.specificRecordsIds).toHaveLength(6)
      expect(unified.specificRecordsIds).toContain('1-oranges')
    })
  })
})
