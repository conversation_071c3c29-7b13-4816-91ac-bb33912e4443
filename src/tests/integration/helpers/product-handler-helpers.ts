import { APIGatewayProxyEvent, Context } from 'aws-lambda'
import { createProduct } from '../../../handlers/product/create-product'
import { getProduct } from '../../../handlers/product/get-product'
import { contextMock } from '@schedulino/aws-lambda-test-utils'

import { parseResponse } from '../utils/response-parser'
import { APIGatewayProxyResultWithBody } from '../types/api-gateway'
import { ResourceType } from '../../../serializers/enums'
import { ProductRes } from '../../../dto/product'
import { updateProduct } from '../../../handlers/product/update-product'
import { deleteProduct } from '../../../handlers/product/delete-product'
import { SearchProductRes } from '../../../dto/product-search'
import { searchProduct } from '../../../handlers/product/search-product'
import { ProductPriceRes } from '../../../dto/product-price'
import { createProductPrice } from '../../../handlers/product-price/create-product-price'
import { getProductPrice } from '../../../handlers/product-price/get-product-price'

export const createTestProduct = async (
  event: APIGatewayProxyEvent,
  context?: Context,
): Promise<APIGatewayProxyResultWithBody<ProductRes>> => {
  if (!context) context = contextMock()

  const result = await createProduct(event, context)

  return {
    ...result,
    body: parseResponse(result, ProductRes, ResourceType.PRODUCT),
  }
}

export const updateTestProduct = async (
  event: APIGatewayProxyEvent,
  context?: Context,
): Promise<APIGatewayProxyResultWithBody<ProductRes>> => {
  if (!context) context = contextMock()

  const result = await updateProduct(event, context)

  return {
    ...result,
    body: parseResponse(result, ProductRes, ResourceType.PRODUCT),
  }
}

export const getTestProduct = async (
  event: APIGatewayProxyEvent,
  context?: Context,
): Promise<APIGatewayProxyResultWithBody<ProductRes>> => {
  if (!context) context = contextMock()

  const result = await getProduct(event, context)

  return {
    ...result,
    body: parseResponse(result, ProductRes, ResourceType.PRODUCT),
  }
}

export const deleteTestProduct = async (
  event: APIGatewayProxyEvent,
  context?: Context,
): Promise<APIGatewayProxyResultWithBody<ProductRes>> => {
  if (!context) context = contextMock()

  const result = await deleteProduct(event, context)

  return {
    ...result,
    body: parseResponse(result, ProductRes, ResourceType.PRODUCT),
  }
}

export const searchTestProduct = async (
  event: APIGatewayProxyEvent,
  context?: Context,
): Promise<APIGatewayProxyResultWithBody<SearchProductRes>> => {
  if (!context) context = contextMock()

  const result = await searchProduct(event, context)

  return {
    ...result,
    body: parseResponse(result, SearchProductRes, ResourceType.PRODUCT),
  }
}

export const createTestProductPrice = async (
  event: APIGatewayProxyEvent,
  context?: Context,
): Promise<APIGatewayProxyResultWithBody<ProductPriceRes>> => {
  if (!context) context = contextMock()

  const result = await createProductPrice(event, context)

  return {
    ...result,
    body: parseResponse(result, ProductPriceRes, ResourceType.PRODUCT_PRICE),
  }
}

export const getTestProductPrice = async (
  event: APIGatewayProxyEvent,
  context?: Context,
): Promise<APIGatewayProxyResultWithBody<ProductPriceRes>> => {
  if (!context) context = contextMock()

  const result = await getProductPrice(event, context)

  return {
    ...result,
    body: parseResponse(result, ProductPriceRes, ResourceType.PRODUCT_PRICE),
  }
}