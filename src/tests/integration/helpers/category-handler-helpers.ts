import { APIGatewayProxyEvent, Context } from 'aws-lambda'
import { contextMock } from '@schedulino/aws-lambda-test-utils'

import { parseResponse } from '../utils/response-parser'
import { APIGatewayProxyResultWithBody } from '../types/api-gateway'
import { ResourceType } from '../../../serializers/enums'
import { createCategory } from '../../../handlers/category/create-category'
import { CategoryRes } from '../../../dto/category'
import { deleteCategory } from '../../../handlers/category/delete-category'
import { getCategory } from '../../../handlers/category/get-category'
import { updateCategory } from '../../../handlers/category/update-category'

export const createTestCategory = async (
  event: APIGatewayProxyEvent,
  context?: Context,
): Promise<APIGatewayProxyResultWithBody<CategoryRes>> => {
  if (!context) context = contextMock()

  const result = await createCategory(event, context)

  return {
    ...result,
    body: parseResponse(result, CategoryRes, ResourceType.CATEGORY),
  }
}

export const getTestCategory = async (
  event: APIGatewayProxyEvent,
  context?: Context,
): Promise<APIGatewayProxyResultWithBody<CategoryRes>> => {
  if (!context) context = contextMock()

  const result = await getCategory(event, context)

  return {
    ...result,
    body: parseResponse(result, CategoryRes, ResourceType.CATEGORY),
  }
}

export const deleteTestCategory = async (
  event: APIGatewayProxyEvent,
  context?: Context,
): Promise<APIGatewayProxyResultWithBody<CategoryRes>> => {
  if (!context) context = contextMock()

  const result = await deleteCategory(event, context)

  return {
    ...result,
    body: parseResponse(result, CategoryRes, ResourceType.CATEGORY),
  }
}

export const updateTestCategory = async (
  event: APIGatewayProxyEvent,
  context?: Context,
): Promise<APIGatewayProxyResultWithBody<CategoryRes>> => {
  if (!context) context = contextMock()

  const result = await updateCategory(event, context)

  return {
    ...result,
    body: parseResponse(result, CategoryRes, ResourceType.CATEGORY),
  }
}
