import { APIGatewayProxyResult } from 'aws-lambda'
import { plainToInstance } from 'class-transformer'
import { ErrorResponse } from '../types/errors'
import Serializer from '../../../serializers'
import { ResourceType } from '../../../serializers/enums'

/**
 * Utility function to parse both success and error responses.
 * Throws an ErrorResponse if the response indicates an error.
 * @param result - The result returned by the Lambda handler
 * @param successType - The class-transformer type for successful responses
 * @returns A parsed response body with the appropriate type (success or throws ErrorResponse)
 */
export const parseResponse = <T>(
  result: APIGatewayProxyResult,
  successType: new () => T,
  resourceType: ResourceType,
): T => {
  if (result.statusCode >= 400 && result.statusCode < 600) {
    const parsedError = JSON.parse(result.body) as ErrorResponse
    throw parsedError
  }

  const deserialized = Serializer.deserialize(resourceType, JSON.parse(result.body || '{}'))

  return plainToInstance(successType, deserialized, { excludeExtraneousValues: true })
}
