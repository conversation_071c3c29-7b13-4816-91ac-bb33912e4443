import { APIGatewayProxyEvent } from 'aws-lambda'
import { apiGatewayEventMock } from '@schedulino/aws-lambda-test-utils'
import Serializer from '../../../serializers'
import { ResourceType } from '../../../serializers/enums'

interface CreateMockEventOptions {
  body?: any
  pathParameters?: Record<string, any>
  queryStringParameters?: Record<string, any>
}

export const createMockEvent = (
  options: CreateMockEventOptions = {},
  resourceType?: ResourceType,
): APIGatewayProxyEvent => {
  const { body, pathParameters, queryStringParameters } = options

  const jsonApiBody = body && resourceType ? Serializer.serialize(resourceType, body) : null

  return {
    ...apiGatewayEventMock(),
    body: jsonApiBody ? JSON.stringify(jsonApiBody) : null,
    pathParameters: pathParameters || null,
    queryStringParameters: queryStringParameters || null,
  } as APIGatewayProxyEvent
}
