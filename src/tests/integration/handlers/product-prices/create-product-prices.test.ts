import { ResourceType } from '../../../../serializers/enums'
import { createTestProduct, createTestProductPrice, getTestProductPrice } from '../../helpers/product-handler-helpers'
import { createMockEvent } from '../../utils/mock-event-utils'
import { ProductType } from '../../../../models/common/enums'
import { CommonConverter } from '../../../../converters/CommonConverter'
import { ProductIdPathReq } from '../../../../dto/product'

describe('Integration tests for createProductPrice Lambda Handler', () => {
  const merchantId = 'blu-abc-123'

  const createProductPayload = createMockEvent(
    {
      body: {
        name: 'Integration Test Product',
        type: ProductType.STANDALONE,
      },
      pathParameters: { merchantId },
    },
    ResourceType.PRODUCT,
  )

  const createProductPricePayload = (companyProductId: string) => createMockEvent(
    {
      body: {
        companyProductId,
        unitPrice: 10.99,
      },
      pathParameters: { merchantId, productId: companyProductId },
    },
    ResourceType.PRODUCT_PRICE,
  )

  it('should create a product and return a 200 response with valid inputs', async () => {
    const { body: createProductRes, statusCode: createProductStatus } =
      await createTestProduct(createProductPayload)
    const { body: createProducPriceRes, statusCode: createSProductPricetatus } = 
      await createTestProductPrice(createProductPricePayload(createProductRes.id))

    expect(createProductStatus).toBe(200)
    expect(createSProductPricetatus).toBe(200)

    const getProductPricePayload = createMockEvent({
      pathParameters: { merchantId, productId: createProducPriceRes.companyProductId },
    })

    const { body: getProductPriceRes, statusCode: getStatus } = await getTestProductPrice(getProductPricePayload)

    const pathReq = CommonConverter.toDto(getProductPricePayload.pathParameters, ProductIdPathReq)

    expect(getStatus).toBe(200)

    expect(getProductPriceRes).toBeDefined()
    expect(getProductPriceRes.companyProductId).toBe(pathReq.productId)
    expect(getProductPriceRes.tenantId).toBe(pathReq.tenantId)
    expect(getProductPriceRes.unitPrice).toBe(10.99)
    expect(new Date(getProductPriceRes.createdAt).toString()).not.toBe('Invalid Date')
    expect(new Date(getProductPriceRes.updatedAt).toString()).not.toBe('Invalid Date')
    expect(getProductPriceRes.retailerPrices).toEqual([])
  })

  it('should throw an ErrorResponse when required fields are missing', async () => {
    const invalidPayload = createMockEvent(
      {
        body: {
          retailerPrices: []
        },
        pathParameters: { merchantId, productId: 'random-id' },
      },
      ResourceType.PRODUCT_PRICE,
    )


    await expect(createTestProductPrice(invalidPayload)).rejects.toMatchObject({
      errors: [
        {
          detail: 'unitPrice should not be empty',
          source: { pointer: '/unitPrice' },
          status: 400,
        },
        {
          detail: 'companyProductId should not be empty',
          source: { pointer: '/companyProductId' },
          status: 400,
        },
      ],
    })
  })

  it('should throw an ErrorResponse for invalid merchantId', async () => {
    const invalidMerchantPayload = createMockEvent(
      {
        body: {
          unitPrice: 10.99,
          companyProductId: 'random-id',
        },
        pathParameters: { merchantId: 'invalid-merchant', productId: 'random-id' },
      },
      ResourceType.PRODUCT_PRICE,
    )

    await expect(createTestProductPrice(invalidMerchantPayload)).rejects.toMatchObject({
      errors: [
        {
          detail:
            'merchant ID does not conform to the following format: {POS}-{tenant ID}-{retailer ID}',
          source: { pointer: '/merchantId' },
          status: 400,
        },
      ],
    })
  })

  it('should throw an ErrorResponse for invalid companyProductId', async () => {
    const invalidTypePayload = createMockEvent(
      {
        body: {
          unitPrice: 10.99,
          companyProductId: 'random-id',
        },
        pathParameters: { merchantId, productId: 'random-id' },
      },
      ResourceType.PRODUCT_PRICE,
    )

    await expect(createTestProductPrice(invalidTypePayload)).rejects.toMatchObject({
      errors: [
        {
          detail: 'Product not found',
          status: 404,
        },
      ],
    })
  })

  it('should set optional fields to undefined when only required fields are specified', async () => {
    const { body: createProductRes } =
      await createTestProduct(createProductPayload)

    const { body: createProducPriceRes, statusCode: createProductPriceStatus } = 
      await createTestProductPrice(createProductPricePayload(createProductRes.id))

    expect(createProductPriceStatus).toBe(200)

    const getProductPricePayload = createMockEvent({
      pathParameters: { merchantId, productId: createProducPriceRes.companyProductId },
    })

    const { body: getProductPriceRes, statusCode: getStatus } = await getTestProductPrice(getProductPricePayload)

    expect(getStatus).toBe(200)

    expect(getProductPriceRes).toBeDefined()
    expect(getProductPriceRes.unitPrice).toBe(10.99)
    expect(new Date(getProductPriceRes.createdAt).toString()).not.toBe('Invalid Date')
    expect(new Date(getProductPriceRes.updatedAt).toString()).not.toBe('Invalid Date')
    expect(getProductPriceRes.retailerPrices).toEqual([])
  })
})
