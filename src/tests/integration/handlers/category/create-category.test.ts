import { ResourceType } from '../../../../serializers/enums'
import { createTestCategory, getTestCategory } from '../../helpers/category-handler-helpers'
import { createMockEvent } from '../../utils/mock-event-utils'

describe('Integration tests for createCategory Handler', () => {
  it('should create a category and return a 200 response with valid inputs', async () => {
    const merchantId = 'blu-abc-124'

    const createCategoryPayload = createMockEvent(
      {
        body: {
          name: 'Integration Test Category',
          image: 'TEST_IMAGE',
          cannabisTypeId: 'CANNABIS_TYPE_ID',
          cannabisTypeName: 'CANNABIS_TYPE_NAME',
          orderIndex: 0,
        },
        pathParameters: { merchantId },
      },
      ResourceType.CATEGORY,
    )

    let { body: createCategoryRes, statusCode: createStatus } =
      await createTestCategory(createCategoryPayload)

    expect(createStatus).toBe(200)

    const getCategoryPayload = createMockEvent({
      pathParameters: { merchantId, categoryId: createCategoryRes.id },
    })

    let { body: getCategoryRes, statusCode: getStatus } = await getTestCategory(getCategoryPayload)

    expect(getStatus).toBe(200)

    expect(getCategoryRes).toBeDefined()
    expect(getCategoryRes.name).toBe('Integration Test Category')
    expect(getCategoryRes.cannabisTypeId).toBe('CANNABIS_TYPE_ID')
    expect(getCategoryRes.cannabisTypeName).toBe('CANNABIS_TYPE_NAME')
    expect(getCategoryRes.isActive).toBe(true)
    expect(getCategoryRes.tenantId).toBe('abc')
    expect(getCategoryRes.updatedAt).toBeDefined()
    expect(getCategoryRes.createdAt).toBeDefined()
    expect(getCategoryRes.measurementType).toBe('Units')
    expect(getCategoryRes.image).toBe('TEST_IMAGE')
    expect(getCategoryRes.orderIndex).toBe(0)
    expect(getCategoryRes.childCategories).toEqual([])
  })

  it('should create a category with a child category and return a 200 response with valid inputs', async () => {
    const merchantId = 'blu-abc-124'

    const createCategoryPayload = createMockEvent(
      {
        body: {
          name: 'Integration Test Category',
          image: 'TEST_IMAGE',
          cannabisTypeId: 'CANNABIS_TYPE_ID',
          cannabisTypeName: 'CANNABIS_TYPE_NAME',
          orderIndex: 0,
          childCategories: [
            {
              name: 'Integration Test Child Category'
            }
          ]
        },
        pathParameters: { merchantId },
      },
      ResourceType.CATEGORY,
    )

    let { body: createCategoryRes, statusCode: createStatus } =
      await createTestCategory(createCategoryPayload)

    expect(createStatus).toBe(200)

    const getCategoryPayload = createMockEvent({
      pathParameters: { merchantId, categoryId: createCategoryRes.id },
    })

    let { body: getCategoryRes, statusCode: getStatus } = await getTestCategory(getCategoryPayload)

    expect(getStatus).toBe(200)

    expect(getCategoryRes).toBeDefined()
    expect(getCategoryRes.name).toBe('Integration Test Category')
    expect(getCategoryRes.cannabisTypeId).toBe('CANNABIS_TYPE_ID')
    expect(getCategoryRes.cannabisTypeName).toBe('CANNABIS_TYPE_NAME')
    expect(getCategoryRes.isActive).toBe(true)
    expect(getCategoryRes.tenantId).toBe('abc')
    expect(getCategoryRes.updatedAt).toBeDefined()
    expect(getCategoryRes.createdAt).toBeDefined()
    expect(getCategoryRes.measurementType).toBe('Units')
    expect(getCategoryRes.image).toBe('TEST_IMAGE')
    expect(getCategoryRes.orderIndex).toBe(0)
    getCategoryRes.childCategories.forEach(childCategory => {
      expect(childCategory.name).toBe('Integration Test Child Category')
      expect(childCategory.isActive).toBe(true)
      expect(childCategory.tenantId).toBe('abc')
      expect(childCategory.parentCategoryId).toBe(createCategoryRes.id)
    })
  })

  it('should return a 400 error when required fields are missing', async () => {
    const merchantId = 'blu-abc-124'
    const invalidPayload = createMockEvent(
      {
        body: {
          data: {
            attributes: {},
          },
        },
        pathParameters: { merchantId },
      },
      ResourceType.CATEGORY,
    )

    await expect(createTestCategory(invalidPayload)).rejects.toMatchObject({
      errors: [
        {
          detail: 'name should not be empty',
          source: { pointer: '/name' },
          status: 400,
        },
        {
          detail: 'cannabisTypeId should not be empty',
          source: { pointer: '/cannabisTypeId' },
          status: 400,
        },
        {
          detail: 'cannabisTypeName should not be empty',
          source: { pointer: '/cannabisTypeName' },
          status: 400,
        },
        {
          detail: 'image should not be empty',
          source: { pointer: '/image' },
          status: 400,
        },
        {
          detail: 'orderIndex should not be empty',
          source: { pointer: '/orderIndex' },
          status: 400,
        },
      ],
    })
  })

  it('should return a 400 error when required fields are missing for child product', async () => {
    const merchantId = 'blu-abc-124'
    const invalidPayload = createMockEvent(
      {
        body: {
          name: 'Integration Test Category',
          image: 'TEST_IMAGE',
          cannabisTypeId: 'CANNABIS_TYPE_ID',
          cannabisTypeName: 'CANNABIS_TYPE_NAME',
          orderIndex: 0,
          childCategories: [
            {
              image: 'Test Image'
            }
          ]
        },
        pathParameters: { merchantId },
      },
      ResourceType.CATEGORY,
    )

    await expect(createTestCategory(invalidPayload)).rejects.toMatchObject({
      errors: [
        {
          detail: 'name should not be empty',
          source: { pointer: '/childCategories/0/name' },
          status: 400,
        },
      ],
    })
  })

  it('should return a 400 error when merchantId is in an invalid format', async () => {
    const invalidPayload = createMockEvent(
      {
        body: {
          name: 'Integration Test Category',
          image: 'TEST_IMAGE',
          cannabisTypeId: 'CANNABIS_TYPE_ID',
          cannabisTypeName: 'CANNABIS_TYPE_NAME',
          orderIndex: 0,
        },
        pathParameters: { merchantId: 'abc' },
      },
      ResourceType.CATEGORY,
    )

    await expect(createTestCategory(invalidPayload)).rejects.toMatchObject({
      errors: [
        {
          detail:
            'merchant ID does not conform to the following format: {POS}-{tenant ID}-{retailer ID}',
          source: { pointer: '/merchantId' },
          status: 400,
        },
      ],
    })
  })
})
