import { ResourceType } from '../../../../serializers/enums'
import {
  createTestCategory,
  deleteTestCategory,
  getTestCategory,
} from '../../helpers/category-handler-helpers'
import { createMockEvent } from '../../utils/mock-event-utils'

describe('Integration tests for deleteCategory Lambda Handler', () => {
  const merchantId = 'blu-abc-124'
  let createdCategoryId: string

  beforeEach(async () => {
    const createCategoryPayload = createMockEvent(
      {
        body: {
          name: 'Category to Delete',
          image: 'TEST_IMAGE',
          cannabisTypeId: 'CANNABIS_TYPE_ID',
          cannabisTypeName: 'CANNABIS_TYPE_NAME',
          orderIndex: 0,
        },
        pathParameters: { merchantId },
      },
      ResourceType.CATEGORY,
    )

    const { body: createCategoryRes, statusCode: createStatus } =
      await createTestCategory(createCategoryPayload)
    expect(createStatus).toBe(200)
    expect(createCategoryRes).toBeDefined()
    createdCategoryId = createCategoryRes.id
  })

  it('should soft delete a category successfully and return a 204 response', async () => {
    const deleteCategoryPayload = createMockEvent(
      {
        pathParameters: { merchantId, categoryId: createdCategoryId },
      },
      ResourceType.CATEGORY,
    )
    const { statusCode: deleteStatus } = await deleteTestCategory(deleteCategoryPayload)
    expect(deleteStatus).toBe(204)

    const getCategoryPayload = createMockEvent({
      pathParameters: { merchantId, categoryId: createdCategoryId },
    })

    const { body: getCategoryRes, statusCode: getCategoryStatus } =
      await getTestCategory(getCategoryPayload)
    expect(getCategoryStatus).toBe(200)
    expect(getCategoryRes).toBeDefined()
    expect(getCategoryRes.deletedAt).toBeDefined()
  })

  it('should soft delete a category and child categories successfully and return a 204 response', async () => {
    const createCategoryPayload = createMockEvent(
      {
        body: {
          name: 'Category to Delete',
          image: 'TEST_IMAGE',
          cannabisTypeId: 'CANNABIS_TYPE_ID',
          cannabisTypeName: 'CANNABIS_TYPE_NAME',
          orderIndex: 0,
          childCategories: [
            {
              name: 'Integration Test Child Category'
            }
          ]
        },
        pathParameters: { merchantId },
      },
      ResourceType.CATEGORY,
    )

    const { body: createCategoryRes, statusCode: createStatus } =
      await createTestCategory(createCategoryPayload)
    expect(createStatus).toBe(200)
    expect(createCategoryRes).toBeDefined()

    const deleteCategoryPayload = createMockEvent(
      {
        pathParameters: { merchantId, categoryId: createCategoryRes.id },
      },
      ResourceType.CATEGORY,
    )
    const { statusCode: deleteStatus } = await deleteTestCategory(deleteCategoryPayload)
    expect(deleteStatus).toBe(204)

    const getCategoryPayload = createMockEvent({
      pathParameters: { merchantId, categoryId: createCategoryRes.id },
    })

    const { body: getCategoryRes, statusCode: getCategoryStatus } =
      await getTestCategory(getCategoryPayload)
    expect(getCategoryStatus).toBe(200)
    expect(getCategoryRes).toBeDefined()
    expect(getCategoryRes.deletedAt).toBeDefined()
    getCategoryRes.childCategories.forEach((childCategory) => {
      expect(childCategory.deletedAt).toBeDefined()
    })
  })

  it('should return a 404 error when attempting to delete a non-existent category', async () => {
    const deleteNonExistentCategoryPayload = createMockEvent(
      {
        pathParameters: { merchantId, categoryId: 'non-existent-category-id' },
      },
      ResourceType.CATEGORY,
    )

    await expect(deleteTestCategory(deleteNonExistentCategoryPayload)).rejects.toMatchObject({
      errors: [
        {
          detail: 'Category not found',
          status: 404,
        },
      ],
    })
  })

  it('should return a 400 error for invalid merchantId', async () => {
    const invalidMerchantPayload = createMockEvent(
      {
        pathParameters: { merchantId: 'invalid-merchant', categoryId: createdCategoryId },
      },
      ResourceType.CATEGORY,
    )

    await expect(deleteTestCategory(invalidMerchantPayload)).rejects.toMatchObject({
      errors: [
        {
          detail:
            'merchant ID does not conform to the following format: {POS}-{tenant ID}-{retailer ID}',
          source: { pointer: '/merchantId' },
          status: 400,
        },
      ],
    })
  })
})
