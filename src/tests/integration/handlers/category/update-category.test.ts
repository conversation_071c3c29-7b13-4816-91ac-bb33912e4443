import { ResourceType } from '../../../../serializers/enums'
import { createTestCategory, getTestCategory, updateTestCategory } from '../../helpers/category-handler-helpers'
import { createMockEvent } from '../../utils/mock-event-utils'

describe('Integration tests for updateCategory Handler', () => {
  const merchantId = 'blu-abc-124'
  const createCategory = async () => {
    const createCategoryPayload = createMockEvent(
      {
        body: {
          name: 'Integration Test Category',
          image: 'TEST_IMAGE',
          cannabisTypeId: 'CANNABIS_TYPE_ID',
          cannabisTypeName: 'CANNABIS_TYPE_NAME',
          orderIndex: 0,
        },
        pathParameters: { merchantId },
      },
      ResourceType.CATEGORY,
    )

    let { body: createCategoryRes, statusCode: createStatus } =
      await createTestCategory(createCategoryPayload)

    expect(createStatus).toBe(200)
    return createCategoryRes.id
  }

  it('should update a category and validate changes in the database', async () => {
    const createdCategoryId = await createCategory()

    const updateCategoryPayload = createMockEvent(
      {
        body: {
          id: createdCategoryId,
          name: 'Integration Test Category',
          image: 'TEST_IMAGE',
          cannabisTypeId: 'CANNABIS_TYPE_ID',
          cannabisTypeName: 'CANNABIS_TYPE_NAME',
          orderIndex: 1,
        },
        pathParameters: { merchantId, categoryId: createdCategoryId },
      },
      ResourceType.CATEGORY,
    )

    let { body: updateCategoryRes, statusCode: createStatus } =
      await updateTestCategory(updateCategoryPayload)

    expect(createStatus).toBe(200)

    const getCategoryPayload = createMockEvent({
      pathParameters: { merchantId, categoryId: updateCategoryRes.id },
    })

    let { body: getCategoryRes, statusCode: getStatus } = await getTestCategory(getCategoryPayload)

    expect(getStatus).toBe(200)

    expect(getCategoryRes).toBeDefined()
    expect(getCategoryRes.name).toBe('Integration Test Category')
    expect(getCategoryRes.cannabisTypeId).toBe('CANNABIS_TYPE_ID')
    expect(getCategoryRes.cannabisTypeName).toBe('CANNABIS_TYPE_NAME')
    expect(getCategoryRes.isActive).toBe(true)
    expect(getCategoryRes.tenantId).toBe('abc')
    expect(getCategoryRes.updatedAt).toBeDefined()
    expect(getCategoryRes.createdAt).toBeDefined()
    expect(getCategoryRes.measurementType).toBe('Units')
    expect(getCategoryRes.image).toBe('TEST_IMAGE')
    expect(getCategoryRes.orderIndex).toBe(1)
    expect(getCategoryRes.childCategories).toEqual([])
  })

  it('should throw an ErrorResponse when required fields are missing', async () => {
    const createdCategoryId = await createCategory()
    const updateCategoryPayload = createMockEvent(
      {
        body: {},
        pathParameters: { merchantId, categoryId: createdCategoryId },
      },
      ResourceType.CATEGORY,
    )
    await expect(updateTestCategory(updateCategoryPayload)).rejects.toMatchObject({
      errors: [
        {
          "detail": "id should not be empty",
          "source": {
            "pointer": "/id",
          },
          "status": 400,
        },
        { detail: 'name should not be empty', source: { pointer: '/name' }, status: 400 },
        {
          "detail": "cannabisTypeId should not be empty",
          "source": {
            "pointer": "/cannabisTypeId",
          },
          "status": 400,
        },
        {
          "detail": "cannabisTypeName should not be empty",
          "source": {
            "pointer": "/cannabisTypeName",
          },
          "status": 400,
        },
        {
          "detail": "image should not be empty",
          "source": {
            "pointer": "/image",
          },
          "status": 400,
        },
        {
          "detail": "orderIndex should not be empty",
          "source": {
            "pointer": "/orderIndex",
          },
          "status": 400,
        }
      ]
    })
  })

  it('should throw an ErrorResponse for invalid categoryId', async () => {
    const invalidCategoryId = 'invalid-category-id'
    const updateCategoryPayload = createMockEvent(
      {
        body: {
          id: invalidCategoryId,
          name: 'Integration Test Category',
          image: 'TEST_IMAGE',
          cannabisTypeId: 'CANNABIS_TYPE_ID',
          cannabisTypeName: 'CANNABIS_TYPE_NAME',
          orderIndex: 1,
        },
        pathParameters: { merchantId, categoryId: invalidCategoryId },
      },
      ResourceType.CATEGORY,
    )
    await expect(updateTestCategory(updateCategoryPayload)).rejects.toMatchObject({
      errors: [
        {
          detail: 'Category not found',
          status: 404,
        },
      ],
    })
  })

  it('should throw an ErrorResponse for invalid merchantId', async () => {
    const createdCategoryId = await createCategory()
    const updateCategoryPayload = createMockEvent(
      {
        body: {
          id: createdCategoryId,
          name: 'Integration Test Category',
          image: 'TEST_IMAGE',
          cannabisTypeId: 'CANNABIS_TYPE_ID',
          cannabisTypeName: 'CANNABIS_TYPE_NAME',
          orderIndex: 1,
        },
        pathParameters: { merchantId: 'invalid-merchant', categoryId: createdCategoryId },
      },
      ResourceType.CATEGORY,
    )
    await expect(updateTestCategory(updateCategoryPayload)).rejects.toMatchObject({
      errors: [
        {
          detail:
            'merchant ID does not conform to the following format: {POS}-{tenant ID}-{retailer ID}',
          source: { pointer: '/merchantId' },
          status: 400,
        },
      ],
    })
  })
})
