import { ResourceType } from '../../../../serializers/enums'
import {
  createTestProduct,
  deleteTestProduct,
  getTestProduct,
} from '../../helpers/product-handler-helpers'
import { createMockEvent } from '../../utils/mock-event-utils'

describe('Integration tests for deleteProduct Lambda Handler (Soft Delete)', () => {
  const merchantId = 'blu-abc-123'
  let createdProductId: string

  beforeAll(async () => {
    const createProductPayload = createMockEvent(
      {
        body: {
          sku: 'SKU_SOFT_DELETE_TEST',
          name: 'Soft Delete Test Product',
          unitPrice: 100,
          type: 'STANDALONE',
          tags: ['delete', 'test'],
          isActive: true,
          isAvailableOnline: true,
          brandId: 'BRAND_SOFT_DEL001',
          description: 'This product will be soft-deleted in the integration test',
          categoryId: 'CATEGORY_SOFT_DEL001',
        },
        pathParameters: { merchantId },
      },
      ResourceType.PRODUCT,
    )

    const { body: createProductRes, statusCode: createStatus } =
      await createTestProduct(createProductPayload)
    expect(createStatus).toBe(200)
    createdProductId = createProductRes.id
  })

  it('should soft delete a product successfully and return a 204 response', async () => {
    const deleteProductPayload = createMockEvent(
      {
        pathParameters: { merchantId, productId: createdProductId },
      },
      ResourceType.PRODUCT,
    )

    const { statusCode: deleteStatus } = await deleteTestProduct(deleteProductPayload)
    expect(deleteStatus).toBe(204)

    const getProductPayload = createMockEvent({
      pathParameters: { merchantId, productId: createdProductId },
    })

    const { body: getProductRes, statusCode: getStatus } = await getTestProduct(getProductPayload)
    expect(getStatus).toBe(200)
    expect(getProductRes).toBeDefined()

    expect(getProductRes.deletedAt).toBeDefined()
    expect(new Date(getProductRes.deletedAt).toString()).not.toBe('Invalid Date')
  })

  it('should throw an ErrorResponse when attempting to delete a non-existent product', async () => {
    const deleteNonExistentProductPayload = createMockEvent(
      {
        pathParameters: { merchantId, productId: 'non-existent-product' },
      },
      ResourceType.PRODUCT,
    )

    await expect(deleteTestProduct(deleteNonExistentProductPayload)).rejects.toMatchObject({
      errors: [
        {
          detail: 'Product not found',
          status: 404,
        },
      ],
    })
  })

  it('should throw an ErrorResponse for invalid merchantId', async () => {
    const invalidMerchantPayload = createMockEvent(
      {
        pathParameters: { merchantId: 'invalid-merchant', productId: createdProductId },
      },
      ResourceType.PRODUCT,
    )

    await expect(deleteTestProduct(invalidMerchantPayload)).rejects.toMatchObject({
      errors: [
        {
          detail:
            'merchant ID does not conform to the following format: {POS}-{tenant ID}-{retailer ID}',
          source: { pointer: '/merchantId' },
          status: 400,
        },
      ],
    })
  })
})
