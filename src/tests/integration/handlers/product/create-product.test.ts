import { ResourceType } from '../../../../serializers/enums'
import { createTestProduct, getTestProduct } from '../../helpers/product-handler-helpers'
import { createMockEvent } from '../../utils/mock-event-utils'
import { ProductType } from '../../../../models/common/enums'
import { CommonConverter } from '../../../../converters/CommonConverter'
import { ProductIdPathReq } from '../../../../dto/product'
import { get } from 'http'

describe('Integration tests for createProduct Lambda Handler', () => {
  const merchantId = 'blu-abc-123'

  const createProductPayload = createMockEvent(
    {
      body: {
        name: 'Integration Test Product',
        type: ProductType.STANDALONE,
        tags: ['integration', 'test'],
        isActive: true,
        isAvailableOnline: true,
        brandId: 'BRAND_INT001',
        description: 'This is a product created during an integration test',
        categoryId: 'CATEGORY_INT001',
        images: [],
        vendorIds: ['VENDOR_INT001'],
        cannabisWeight: 1,
        cannabisWeightUnit: 'g',
      },
      pathParameters: { merchantId },
    },
    ResourceType.PRODUCT,
  )

  it('should create a product and return a 200 response with valid inputs', async () => {
    const { body: createProductRes, statusCode: createStatus } =
      await createTestProduct(createProductPayload)

    expect(createStatus).toBe(200)

    const getProductPayload = createMockEvent({
      pathParameters: { merchantId, productId: createProductRes.id },
    })

    const { body: getProductRes, statusCode: getStatus } = await getTestProduct(getProductPayload)

    const pathReq = CommonConverter.toDto(getProductPayload.pathParameters, ProductIdPathReq)

    expect(getStatus).toBe(200)

    expect(getProductRes).toBeDefined()
    expect(getProductRes.id).toBe(pathReq.productId)
    expect(getProductRes.tenantId).toBe(pathReq.tenantId)
    expect(getProductRes.pos).toBe(pathReq.pos)
    expect(getProductRes.name).toBe('Integration Test Product')
    expect(getProductRes.type).toBe(ProductType.STANDALONE)
    expect(getProductRes.tags).toEqual(['integration', 'test'])
    expect(getProductRes.isActive).toBe(true)
    expect(getProductRes.isAvailableOnline).toBe(true)
    expect(getProductRes.brandId).toBe('BRAND_INT001')
    expect(getProductRes.description).toBe('This is a product created during an integration test')
    expect(getProductRes.categoryId).toBe('CATEGORY_INT001')
    expect(new Date(getProductRes.createdAt).toString()).not.toBe('Invalid Date')
    expect(new Date(getProductRes.updatedAt).toString()).not.toBe('Invalid Date')
    expect(getProductRes.images).toEqual([])
    expect(getProductRes.vendorIds).toEqual(['VENDOR_INT001'])
    expect(getProductRes.cannabisWeight).toBe(1)
    expect(getProductRes.cannabisWeightUnit).toBe('g')
  })

  it('should throw an ErrorResponse when required fields are missing', async () => {
    const invalidPayload = createMockEvent(
      {
        body: {
          description: 'Lorem ipsom',
        },
        pathParameters: { merchantId },
      },
      ResourceType.PRODUCT,
    )

    await expect(createTestProduct(invalidPayload)).rejects.toMatchObject({
      errors: [
        {
          detail: 'name should not be empty',
          source: { pointer: '/name' },
          status: 400,
        },
        {
          detail: 'type must be one of the following values: STANDALONE, VARIANT, PARENT',
          source: { pointer: '/type' },
          status: 400,
        },
      ],
    })
  })

  it('should throw an ErrorResponse for invalid merchantId', async () => {
    const invalidMerchantPayload = createMockEvent(
      {
        body: {
          name: 'Invalid Merchant Product',
          type: ProductType.STANDALONE,
        },
        pathParameters: { merchantId: 'invalid-merchant' },
      },
      ResourceType.PRODUCT,
    )

    await expect(createTestProduct(invalidMerchantPayload)).rejects.toMatchObject({
      errors: [
        {
          detail:
            'merchant ID does not conform to the following format: {POS}-{tenant ID}-{retailer ID}',
          source: { pointer: '/merchantId' },
          status: 400,
        },
      ],
    })
  })

  it('should throw an ErrorResponse for invalid product type', async () => {
    const invalidTypePayload = createMockEvent(
      {
        body: {
          name: 'Invalid Type Product',
          type: 'INVALID_TYPE',
        },
        pathParameters: { merchantId },
      },
      ResourceType.PRODUCT,
    )

    await expect(createTestProduct(invalidTypePayload)).rejects.toMatchObject({
      errors: [
        {
          detail: 'type must be one of the following values: STANDALONE, VARIANT, PARENT',
          source: { pointer: '/type' },
          status: 400,
        },
      ],
    })
  })

  it('should set optional fields to undefined when only required fields are specified', async () => {
    const requiredFieldsPayload = createMockEvent(
      {
        body: {
          name: 'Required Fields Product',
          type: ProductType.STANDALONE,
        },
        pathParameters: { merchantId },
      },
      ResourceType.PRODUCT,
    )

    const { body: createProductRes, statusCode: createStatus } =
      await createTestProduct(requiredFieldsPayload)

    expect(createStatus).toBe(200)

    const getProductPayload = createMockEvent({
      pathParameters: { merchantId, productId: createProductRes.id },
    })

    const { body: getProductRes, statusCode: getStatus } = await getTestProduct(getProductPayload)

    expect(getStatus).toBe(200)

    expect(getProductRes).toBeDefined()
    expect(getProductRes.name).toBe('Required Fields Product')
    expect(getProductRes.type).toBe(ProductType.STANDALONE)
    expect(getProductRes.tags).toEqual([])
    expect(getProductRes.isActive).toBe(false)
    expect(getProductRes.isAvailableOnline).toBe(false)
    expect(getProductRes.brandId).toBeUndefined()
    expect(getProductRes.description).toBeUndefined()
    expect(getProductRes.categoryId).toBeUndefined()
    expect(new Date(getProductRes.createdAt).toString()).not.toBe('Invalid Date')
    expect(new Date(getProductRes.updatedAt).toString()).not.toBe('Invalid Date')
    expect(getProductRes.images).toEqual([])
    expect(getProductRes.tags).toEqual([])
    expect(getProductRes.vendorIds).toEqual([])
    expect(getProductRes.cannabisWeight).toBeUndefined()
    expect(getProductRes.cannabisWeightUnit).toBeUndefined()
    expect(getProductRes.liThreshold).toBeUndefined()
    expect(getProductRes.sku).toBeUndefined()
    expect(getProductRes.retailerOverrides).toEqual([])
  })
})
