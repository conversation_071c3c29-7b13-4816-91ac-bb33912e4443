import { ResourceType } from '../../../../serializers/enums'
import {
  createTestProduct,
  updateTestProduct,
  getTestProduct,
} from '../../helpers/product-handler-helpers'
import { createMockEvent } from '../../utils/mock-event-utils'
import { ProductType } from '../../../../models/common/enums'

describe('Integration tests for updateProduct Lambda Handler', () => {
  const merchantId = 'blu-abc-123'

  const createProduct = async () => {
    const createProductPayload = createMockEvent(
      {
        body: {
          name: 'Common Test Product',
          type: ProductType.STANDALONE,
          tags: ['common', 'test'],
          isActive: true,
          isAvailableOnline: true,
          brandId: 'BRAND_COMMON',
          description: 'This is a common product used for independent tests',
          categoryId: 'CATEGORY_COMMON',
        },
        pathParameters: { merchantId },
      },
      ResourceType.PRODUCT,
    )

    const { body: createProductRes, statusCode: createStatus } =
      await createTestProduct(createProductPayload)
    expect(createStatus).toBe(200)
    return createProductRes.id
  }

  it('should update a product and validate changes in the database', async () => {
    const createdProductId = await createProduct()

    const updateProductPayload = createMockEvent(
      {
        body: {
          name: 'Updated Test Product',
          type: ProductType.VARIANT,
          tags: ['updated', 'test'],
          isActive: false,
          isAvailableOnline: false,
          brandId: 'BRAND_UPDATED',
          description: 'This product has been updated in the test',
          categoryId: 'CATEGORY_UPDATED',
          vendorIds: ['VENDOR_COMMON'],
          cannabisWeight: 3.5,
          cannabisWeightUnit: 'g',
          liThreshold: 10,
          sku: 'SKU_UPDATED',
          retailerOverrides: [
            {
              retailerId: 'RETAILER_UPDATED',
              sku: 'SKU_UPDATED',
              liThreshold: 10,
              isActive: false,
            },
          ],
        },
        pathParameters: { merchantId, productId: createdProductId },
      },
      ResourceType.PRODUCT,
    )

    const { statusCode: updateStatus } = await updateTestProduct(updateProductPayload)
    expect(updateStatus).toBe(200)

    const getProductPayload = createMockEvent({
      pathParameters: { merchantId, productId: createdProductId },
    })
    const { body: getProductRes, statusCode: getStatus } = await getTestProduct(getProductPayload)
    expect(getStatus).toBe(200)
    expect(getProductRes.name).toBe('Updated Test Product')
    expect(getProductRes.type).toBe(ProductType.VARIANT)
    expect(getProductRes.tags).toEqual(['updated', 'test'])
    expect(getProductRes.isActive).toBe(false)
    expect(getProductRes.isAvailableOnline).toBe(false)
    expect(getProductRes.brandId).toBe('BRAND_UPDATED')
    expect(getProductRes.description).toBe('This product has been updated in the test')
    expect(getProductRes.categoryId).toBe('CATEGORY_UPDATED')
    expect(new Date(getProductRes.createdAt).toString()).not.toBe('Invalid Date')
    expect(new Date(getProductRes.updatedAt).toString()).not.toBe('Invalid Date')
    expect(getProductRes.vendorIds).toEqual(['VENDOR_COMMON'])
    expect(getProductRes.cannabisWeight).toBe(3.5)
    expect(getProductRes.cannabisWeightUnit).toBe('g')
    expect(getProductRes.liThreshold).toBe(10)
    expect(getProductRes.sku).toBe('SKU_UPDATED')
    expect(getProductRes.retailerOverrides).toEqual([{
      retailerId: 'RETAILER_UPDATED',
      sku: 'SKU_UPDATED',
      liThreshold: 10,
      isActive: false,
    }])
  })

  it('should throw an ErrorResponse when required fields are missing', async () => {
    const createdProductId = await createProduct()

    const invalidPayload = createMockEvent(
      {
        body: {
          description: 'Invalid update payload',
        },
        pathParameters: { merchantId, productId: createdProductId },
      },
      ResourceType.PRODUCT,
    )

    await expect(updateTestProduct(invalidPayload)).rejects.toMatchObject({
      errors: [
        { detail: 'name should not be empty', source: { pointer: '/name' }, status: 400 },
        {
          detail: 'type must be one of the following values: STANDALONE, VARIANT, PARENT',
          source: { pointer: '/type' },
          status: 400,
        },
      ],
    })
  })

  it('should throw an ErrorResponse for invalid productId', async () => {
    const invalidProductIdPayload = createMockEvent(
      {
        body: {
          name: 'Invalid Product Update',
          type: ProductType.VARIANT,
        },
        pathParameters: { merchantId, productId: 'invalid-product-id' },
      },
      ResourceType.PRODUCT,
    )

    await expect(updateTestProduct(invalidProductIdPayload)).rejects.toMatchObject({
      errors: [
        {
          detail: 'Product not found',
          status: 404,
        },
      ],
    })
  })

  it('should throw an ErrorResponse for invalid merchantId', async () => {
    const createdProductId = await createProduct()

    const invalidMerchantPayload = createMockEvent(
      {
        body: {
          name: 'Invalid Merchant Update',
          type: ProductType.VARIANT,
        },
        pathParameters: { merchantId: 'invalid-merchant', productId: createdProductId },
      },
      ResourceType.PRODUCT,
    )

    await expect(updateTestProduct(invalidMerchantPayload)).rejects.toMatchObject({
      errors: [
        {
          detail:
            'merchant ID does not conform to the following format: {POS}-{tenant ID}-{retailer ID}',
          source: { pointer: '/merchantId' },
          status: 400,
        },
      ],
    })
  })

  it('should throw an ErrorResponse for invalid product type', async () => {
    const createdProductId = await createProduct()

    const invalidTypePayload = createMockEvent(
      {
        body: {
          name: 'Invalid Type Update',
          type: 'INVALID_TYPE',
        },
        pathParameters: { merchantId, productId: createdProductId },
      },
      ResourceType.PRODUCT,
    )

    await expect(updateTestProduct(invalidTypePayload)).rejects.toMatchObject({
      errors: [
        {
          detail: 'type must be one of the following values: STANDALONE, VARIANT, PARENT',
          source: { pointer: '/type' },
          status: 400,
        },
      ],
    })
  })

  it('should set optional fields to undefined when only required fields are specified', async () => {
    const createdProductId = await createProduct()

    const partialUpdatePayload = createMockEvent(
      {
        body: {
          name: 'Required Only Product',
          type: ProductType.STANDALONE,
        },
        pathParameters: { merchantId, productId: createdProductId },
      },
      ResourceType.PRODUCT,
    )

    const { statusCode: updateStatus } = await updateTestProduct(partialUpdatePayload)
    expect(updateStatus).toBe(200)

    const getProductPayload = createMockEvent({
      pathParameters: { merchantId, productId: createdProductId },
    })
    const { body: getProductRes, statusCode: getStatus } = await getTestProduct(getProductPayload)
    expect(getStatus).toBe(200)

    expect(getProductRes.name).toBe('Required Only Product')
    expect(getProductRes.type).toBe(ProductType.STANDALONE)

    expect(getProductRes.tags).toEqual([])
    expect(getProductRes.isActive).toBe(false)
    expect(getProductRes.isAvailableOnline).toBe(false)
    expect(getProductRes.brandId).toBeUndefined()
    expect(getProductRes.description).toBeUndefined()
    expect(getProductRes.categoryId).toBeUndefined()
    expect(getProductRes.images).toEqual([])

    expect(new Date(getProductRes.createdAt).toString()).not.toBe('Invalid Date')
    expect(new Date(getProductRes.updatedAt).toString()).not.toBe('Invalid Date')
    expect(getProductRes.tags).toEqual([])
    expect(getProductRes.vendorIds).toEqual([])
    expect(getProductRes.cannabisWeight).toBeUndefined()
    expect(getProductRes.cannabisWeightUnit).toBeUndefined()
    expect(getProductRes.retailerOverrides).toEqual([])
    expect(getProductRes.sku).toBeUndefined()
    expect(getProductRes.liThreshold).toBeUndefined()
  })
})
