import { ValidationError, validateSync } from 'class-validator'
import createHTTPError from 'http-errors'
import { ResponseWriter } from './web/ResponseWriter'
import { EntityType } from '../models/common/enums'

type ErrorCodesTypes = ErrorCodes

export interface IErrorMessage {
  code?: ErrorCodesTypes
  detail: string
  meta?: {
    fields?: string[]
    [key: string]: any
  }
  source?: {
    pointer: string
  }
  status: number
}

/*
 * Applicational error codes
 */

export enum ErrorCodes {
  INVALID_PARAMETER = 'invalid_parameter',
  INVALID_MERCHANT_ID = 'invalid_merchant_id',
  INVALID_LOG_STATUS = 'invalid_log_status',
  INVALID_OPERATION = 'invalid_operation',

  MISSING_LOG_STATUS = 'missing_log_status',
  MISSING_BROADCAST_ID = 'missing_broadcast_id',

  OPERATION_NOT_SUPPORTED = 'operation_not_supported',
  RESOURCE_NOT_FOUND = 'resource_not_found',
}

const notFoundError = (detail: string, code?: ErrorCodes, extra?: Partial<IErrorMessage>) => {
  return ResponseWriter.errorResponse(404, {
    code: code || ErrorCodes.RESOURCE_NOT_FOUND,
    detail,
    status: 404,
    ...extra,
  })
}
const badRequestError = (detail: string, code?: ErrorCodes, extra?: Partial<IErrorMessage>) => {
  return ResponseWriter.errorResponse(400, {
    detail,
    code,
    status: 400,
    ...extra,
  })
}

const invalidOperation = (detail: string, code?: ErrorCodes, extra?: Partial<IErrorMessage>) => {
  return ResponseWriter.errorResponse(400, {
    code: code || ErrorCodes.INVALID_OPERATION,
    detail,
    status: 400,
    ...extra,
  })
}

const notImplementedError = (detail: string, code?: ErrorCodes, extra?: Partial<IErrorMessage>) => {
  return ResponseWriter.errorResponse(501, {
    code: code || ErrorCodes.INVALID_PARAMETER,
    detail,
    status: 501,
    ...extra,
  })
}

export const unknownMerchantPos = () => {
  return invalidMerchantId('Unknown Merchant POS')
}

export const invalidMerchantId = (detail: string) => {
  return badRequestError(detail, ErrorCodes.INVALID_MERCHANT_ID)
}

export const missingDBRecordError = (recordType: EntityType, data = {}) => {
  return notFoundError(`${recordType} record not found`, undefined, {
    meta: {
      [recordType]: data,
    },
  })
}

export const invalidParameterError = (detail: string) => {
  return badRequestError(detail)
}

export const productNotFoundError = () => {
  return notFoundError('Product not found')
}

export const productPriceNotFoundError = () => {
  return notFoundError('Product prices not found')
}

export const categoryNotFoundError = () => {
  return notFoundError('Category not found')
}

const getValidationErrorDetails = (
  errors: ValidationError[],
  keyName: string,
  pointerRootPath?: string | null,
) => {
  const constraints: IErrorMessage[] = []
  const visited = new Set()
  const errorMessages = new Set<string>()

  const targets = [...errors]

  const pointers: string[] = [pointerRootPath || '']

  while (targets.length) {
    const target = targets.shift()

    if (target === undefined) {
      continue
    }

    if (visited.has(target)) {
      continue // skip circular references
    }

    visited.add(target)

    const mappedPairs = new Map(Object.entries(target))

    for (const [key, value] of mappedPairs.entries()) {
      if (key === 'property') {
        const childrenErrorsExist = mappedPairs.get('children')?.length > 0

        if (childrenErrorsExist) {
          pointers.push(value)
        }
      }

      if (Object.keys(value || {}).includes(keyName)) {
        const message = Object.values(value.constraints)[0] as string

        if (errorMessages.has(message)) continue

        const pointer = [...pointers, value.property].join('/')

        constraints.push({
          detail: message,
          source: pointerRootPath === null ? undefined : { pointer },
          status: 400,
        })

        errorMessages.add(message)
      } else if (key === keyName) {
        const message = Object.values(value)[0] as string

        if (errorMessages.has(message)) continue

        const pointer = [...pointers, target.property].join('/')

        constraints.push({
          detail: message,
          source: pointerRootPath === null ? undefined : { pointer },
          status: 400,
        })

        errorMessages.add(message)
      }

      if (value && typeof value === 'object') {
        targets.push(value) // include nested
      }
    }
  }

  return constraints
}

export const validateClass = (c: object, pointerRootPath?: string | null) => {
  const validationErrors = validateSync(c)

  if (validationErrors.length > 0) {
    const message = getValidationErrorDetails(validationErrors, 'constraints', pointerRootPath)

    throw ResponseWriter.errorResponse(400, message)
  }
}

export const createError = createHTTPError
