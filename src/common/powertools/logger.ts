import { Logger } from '@aws-lambda-powertools/logger'
import { config } from './config'
import { PersistentLogKeys } from './enums'

export const logger = new Logger({
  serviceName: config.serviceName,
  logLevel: 'info',
})

export const persistLogAttributes = (attributes: Array<{ key: PersistentLogKeys, value: unknown }>) => {
  attributes.forEach((attribute) => {
    logger.appendKeys({ 
      [attribute.key]: attribute.value ?? null
    })
  })
}

export const removeAllLogAttributes = () => {
  const attributes = logger.getPersistentLogAttributes()
  const keys = Object.keys(attributes) as PersistentLogKeys[]
  removeLogAttributes(keys)
}

export const removeLogAttributes = (keys: PersistentLogKeys[]) => {
  logger.removeKeys(keys)
}

logger.injectLambdaContext()