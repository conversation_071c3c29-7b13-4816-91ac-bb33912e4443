export enum PersistentLogKeys {
  RESOURCE = 'resource',
  COMMAND_TYPE = 'command_type',

  EVENT_ID = 'event_id',
  EVENT_TYPE = 'event_type',
  EVENT_SOURCE = 'event_source',

  SALE_ID = 'sale_id',
  SALE_VERSION_ID = 'sale_version_id',
  STORAGE_ID = 'storage_id',
  TRANSFER_ID = 'transfer_id',
  MERCHANT_ID = 'merchant_id',

  ZERO_REPORT_THRESHOLD = 'zero_report_threshold',
  COUNTRY_CODE = 'country_code',
  STATE_OR_PROVINCE = 'state_or_province',
  HTTP_STATUS = 'http_status',
}
