import 'reflect-metadata'
import middy from '@middy/core'
import httpErrorHandler from '@middy/http-error-handler'
import cors from '@middy/http-cors'
import { Handler } from 'aws-lambda'
import { injectLambdaContext } from '@aws-lambda-powertools/logger/middleware'
import { captureLambdaHandler } from '@aws-lambda-powertools/tracer/middleware'
import { logMetrics } from '@aws-lambda-powertools/metrics/middleware'
import { metrics } from '../powertools/metrics'
import { tracer } from '../powertools/tracer'
import { logger } from '../powertools/logger'
import { Stage } from './enums'
import { LoggerMiddleware } from '../../middlewares/logger-middleware'

export const middyWrapper = <T>(
  handler: Handler<T>,
  {
    setCors = true,
    isHttp = true,
    otherMiddlewares,
  }: {
    setCors?: boolean
    isHttp?: boolean
    otherMiddlewares?: middy.MiddlewareObj[]
  } = {},
) => {
  // https://middy.js.org/docs/intro/testing/
  const timeout = process.env.STAGE === Stage.TEST ? 0 : undefined
  const middifiedHandler = middy(handler, { timeoutEarlyInMillis: timeout })

  if (process.env.STAGE !== Stage.TEST) {
    middifiedHandler
      .use(injectLambdaContext(logger))
      .use(captureLambdaHandler(tracer))
      .use(logMetrics(metrics, { captureColdStartMetric: true }))
  }

  middifiedHandler.use(LoggerMiddleware)

  // do not enable cors and http error handler for SQS handlers
  if (setCors) {
    const allowedHeaders = [
      ...Object.values(Headers),
      'Content-Type',
      'X-Amz-Date',
      'Authorization',
      'X-Api-Key',
      'X-Amz-Security-Token',
    ].join(',')
    const corsMiddleware = cors({
      origins: ['*'],
      credentials: true,
      headers: allowedHeaders,
      methods: 'OPTIONS,GET,POST,PATCH,PUT,DELETE',
    })

    middifiedHandler.use(corsMiddleware)
  }

  if (isHttp) middifiedHandler.use(httpErrorHandler())
  if (otherMiddlewares) middifiedHandler.use(otherMiddlewares)

  return middifiedHandler
}
