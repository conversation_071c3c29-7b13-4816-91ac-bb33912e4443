import {
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  isNotEmpty,
} from 'class-validator'

@ValidatorConstraint({ name: 'merchantId', async: false })
export class CustomMerchantIdValidator implements ValidatorConstraintInterface {
  validate = (text: string) => {
    const [pos, tenantId, retailerId] = text.trim().split('-')

    return isNotEmpty(pos) && isNotEmpty(tenantId) && isNotEmpty(retailerId)
  }

  defaultMessage = (args: ValidationArguments) => {
    return 'merchant ID does not conform to the following format: {POS}-{tenant ID}-{retailer ID}'
  }
}
