export enum PrimaryKeys {
  PK = 'pk',
  SK = 'sk',
}

export enum CompositeKeyNames {
  TYPE = 'type',
  MERCHANT = 'merchant',
  COUNTRY = 'country',
  STATE_OR_PROVINCE = 'sp',
  SALE = 'sale',
  SALE_VERSION = 'sale_version',
  YYYYMM = 'yyyymm',
  STATUS = 'status',
  SUBMITTED_AT = 'submitted_at',
  SALE_COMPLETED_AT = 'sale_completed_at',
}

export enum DynamodbGSI {
  ID_RECORD_TYPE = 'gsi-id-recordType',
  ID_RECORD_STATUS_DATE = 'gsi-id-recordStatusDate',
}

export enum DynamodbIndexKeys {
  ID = 'id',
  RECORD_STATUS_DATE = 'recordStatusDate',
}

export enum DynamodbErrorCode {
  CONDITIONAL_CHECK_FAILED_EXCEPTION = 'ConditionalCheckFailedException',
}
