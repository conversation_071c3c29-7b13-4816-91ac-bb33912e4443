import * as DynamooseClient from 'dynamoose'
import { Stage } from '../../middy/enums'

if ([Stage.LOCAL, Stage.TEST].includes(`${process.env.STAGE}` as Stage)) {
  const ddb = new DynamooseClient.aws.ddb.DynamoDB({
    credentials: {
      accessKeyId: `${process.env.AWS_ACCESS_KEY_ID}`,
      secretAccessKey: `${process.env.AWS_SECRET_ACCESS_KEY}`,
      sessionToken: `${process.env.AWS_SESSION_TOKEN}`,
    },
    region: `${process.env.AWS_DEFAULT_REGION}`,
    endpoint: 'http://localhost:4566'
  })
  DynamooseClient.aws.ddb.set(ddb)
}

export const dynamoose = DynamooseClient
