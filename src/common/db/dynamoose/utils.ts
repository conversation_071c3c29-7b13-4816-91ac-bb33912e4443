import { Condition } from 'dynamoose/dist/Condition'
import { Model } from 'dynamoose/dist/Model'

import { RecordType } from '../../../models/common/enums'
import { IDynamodbQueryParams } from './interfaces'

export const buildQuery = (
  model: Model,
  params: IDynamodbQueryParams & { condition: Condition },
) => {
  const query = model.query(params.condition)

  if (params.limit) query.limit(params.limit)
  if (params.gsi) query.using(params.gsi)
  if (params.sortOrder) query.sort(params.sortOrder)
  if (params.lastKey) query.startAt(params.lastKey)

  return query
}

export const generateKey = (...args: (string | RecordType)[]): string => {
  return args.join('#')
}

export const generateDBKeyV2 = <IContract>(
  keyObjects: { [K in keyof IContract]?: string | number }[],
): string => {
  return keyObjects
    .map((keyObject) =>
      Object.entries(keyObject).map(([key, value]) => `${key}${value ? `#${value}` : ''}`),
    )
    .join('#')
}
