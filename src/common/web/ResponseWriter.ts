import { APIGatewayProxyResult } from 'aws-lambda'
import { IErrorMessage, createError } from '../error'
import { HttpStatusCode } from 'axios'
import { ContentType } from '../../models/common/enums'

export class ResponseWriter {
  static objectResponse(statusCode: HttpStatusCode.NoContent): APIGatewayProxyResult
  static objectResponse(statusCode: HttpStatusCode, rawBody: object | string): APIGatewayProxyResult
  static objectResponse(
    statusCode: HttpStatusCode,
    rawBody?: object | string,
  ): APIGatewayProxyResult {
    let stringBody = ''

    if (rawBody) {
      stringBody = typeof rawBody === 'object' ? JSON.stringify(rawBody) : rawBody
    }

    return {
      statusCode,
      body: stringBody,
      headers: {
        'Content-Type': ContentType.JSON,
      },
    }
  }

  public static generateErrorMessage = (message: IErrorMessage | IErrorMessage[]) => {
    return {
      errors: Array.isArray(message) ? message : [message],
    }
  }

  public static errorResponse = (statusCode: number, message: IErrorMessage | IErrorMessage[]) => {
    const stringifiedMessage = JSON.stringify(this.generateErrorMessage(message))

    return new createError[statusCode](stringifiedMessage)
  }
}
