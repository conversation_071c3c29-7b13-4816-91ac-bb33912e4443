import { SFNClient, StartExecutionCommand } from '@aws-sdk/client-sfn'

export const startStateMachineExecution = async (
  stateMachineArn: string,
  input: Record<string, unknown>,
): Promise<{ executionArn?: string }> => {
  const client = new SFNClient()

  const command = new StartExecutionCommand({
    stateMachineArn,
    input: JSON.stringify(input),
  })

  const response = await client.send(command)

  return {
    executionArn: response.executionArn,
  }
}
