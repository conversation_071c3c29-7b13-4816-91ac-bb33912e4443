import { transferClient } from '../clients'
import { StartFileTransferCommand } from '@aws-sdk/client-transfer'

export const startFileTransfer = async (sendFilePaths: string[], remoteDirecotoryPath: string) => {
  const command = new StartFileTransferCommand({
    ConnectorId: `${process.env.TRANSFER_CONNECTOR_ID}`,
    SendFilePaths: sendFilePaths,
    RemoteDirectoryPath: remoteDirecotoryPath,
  })

  return transferClient.send(command)
}