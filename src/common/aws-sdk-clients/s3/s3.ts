import { GetObjectCommand } from '@aws-sdk/client-s3'
import { s3Client } from '../clients'

export const getS3Object = async (bucketName: string, key: string, versionId?: string) => {
  const command = new GetObjectCommand({
    Bucket: bucketName,
    Key: key,
    VersionId: versionId,
  })
  return s3Client.send(command)
}

export const getS3ContentString = async (bucketName: string, key: string) => {
  const csvFile = await getS3Object(bucketName, key)
  const csvString = await csvFile.Body?.transformToString()
  if (!csvString) throw new Error('CSV file is empty')

  return csvString
}
