import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager'

export const getSecretValue = async (
  secretName: string,
): Promise<Record<string, unknown> | undefined> => {
  const client = new SecretsManagerClient()

  const command = new GetSecretValueCommand({
    SecretId: secretName,
  })

  const response = await client.send(command)

  if (response.SecretString) return JSON.parse(response.SecretString)
  if (response.SecretBinary) return JSON.parse(response.SecretBinary.toString())
}
