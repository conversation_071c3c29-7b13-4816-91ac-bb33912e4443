import { Client } from '@opensearch-project/opensearch'
import { AwsSigv4Signer } from '@opensearch-project/opensearch/aws'
import { defaultProvider } from '@aws-sdk/credential-provider-node'

export const opensearchClient = new Client({
  ...AwsSigv4Signer({
    region: 'us-east-1',
    service: 'aoss',
    getCredentials: () => {
      const credentialsProvider = defaultProvider()
      return credentialsProvider()
    },
  }),
  requestTimeout: 30000,
  node: process.env.AOSS_ENDPOINT,
})
