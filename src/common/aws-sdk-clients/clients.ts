import { S3Client } from '@aws-sdk/client-s3'
import { SQSClient } from '@aws-sdk/client-sqs'
import { TransferClient } from '@aws-sdk/client-transfer'
import { EventBridgeClient } from '@aws-sdk/client-eventbridge'

/**
 * sdk retry logic explaine here:
 * https://docs.aws.amazon.com/sdkref/latest/guide/feature-retry-behavior.html
 */
const commonConfig = {
  maxAttempts: 5,
  retryMode: 'standard',
}

export const s3Client = new S3Client({
  ...commonConfig,
})

export const sqsClient = new SQSClient({
  ...commonConfig,
})

export const transferClient = new TransferClient({
  ...commonConfig,
})

export const eventBridgeClient = new EventBridgeClient({
  ...commonConfig,
})
