type TFulfilledResults<T, U> = U extends true ? T[] : PromiseFulfilledResult<T | T[]>[]

export const splitPromiseSettledResults = <T, U extends boolean>(
  allSettledSQSResults: PromiseSettledResult<T | T[]>[],
  shouldCollapseFulfilledResults: U
): {
  fulfilledResults: TFulfilledResults<T, U>
  rejectedResults: PromiseRejectedResult[]
} => {
  const promiseFulfilledResults: PromiseFulfilledResult<T | T[]>[] = []
  const rejectedResults: PromiseRejectedResult[] = []

  allSettledSQSResults.forEach((result) => {
    result.status === 'fulfilled' ? promiseFulfilledResults.push(result) : rejectedResults.push(result)
  })

  const fulfilledResults = shouldCollapseFulfilledResults
    ? promiseFulfilledResults.flatMap((result) => result.value)
    : promiseFulfilledResults

  return {
    fulfilledResults: fulfilledResults as TFulfilledResults<T, U>,
    rejectedResults
  }
}