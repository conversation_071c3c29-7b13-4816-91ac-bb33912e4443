import { unknownMerchantPos } from '../error'
import { CompositeKeyNames } from '../db/dynamoose/enums'
import { CountryCode } from '../../models/common/enums'
import { IPrimarykeys } from '../../models/common/interfaces'

const merchantCountryCodeMap = {
  [CountryCode.US]: 'blu',
  [CountryCode.CA]: 'blc',
}

export const getCountryCodeFromMerchantId = (merchantId: string) => {
  const { pos } = splitMerchantId(merchantId)
  const entry = Object.entries(merchantCountryCodeMap).find(
    ([countryCode, merchantPos]) => pos === merchantPos,
  )
  const countryCode = entry?.[0] as CountryCode | undefined
  const merchantPos = merchantCountryCodeMap[`${countryCode}`]
  if (!countryCode || !merchantPos) throw unknownMerchantPos()
  return countryCode
}

export const splitMerchantId = (merchantId?: string) => {
  if (!merchantId) return {}

  const [pos, tenantId, retailerId] = merchantId.split('-').filter((item) => item !== '')
  return { pos, tenantId, retailerId }
}

export const buildMerchantId = (params: {
  countryCode: CountryCode
  tenantId: string
  retailerId: string
}) => {
  const merchantPos = merchantCountryCodeMap[params.countryCode]
  if (!merchantPos) throw unknownMerchantPos()
  return [merchantPos, params.tenantId, params.retailerId].join('-')
}

export const takeKeys = (obj: object, keys: any[]) => {
  return keys.reduce((result, key) => {
    result[key] = obj[key]
    return result
  }, {})
}

interface IKeyValuePair {
  key: CompositeKeyNames
  value?: string | number | boolean
}

export const keyGenerator = () => {
  const keyValuePairs: IKeyValuePair[] = []
  const pairJoiner = '#'
  const keyValueJoiner = '.'

  return {
    add: function (keyValuePair: IKeyValuePair | IKeyValuePair[]) {
      const pairs: IKeyValuePair[] = Array.isArray(keyValuePair) ? keyValuePair : [keyValuePair]
      keyValuePairs.push(...pairs)
      return this
    },
    show: function () {
      return keyValuePairs
    },
    joinPair: function (arr: any[]) {
      return arr.join(pairJoiner)
    },
    parsePair: function (str: string) {
      return str.split(pairJoiner)
    },
    joinKeyValue: function (arr: any[]) {
      return arr.join(keyValueJoiner)
    },
    parseKeyValue: function (str: string) {
      return str.split(keyValueJoiner)
    },
    generateKey: function () {
      const arr = keyValuePairs.map(({ key, value }) => {
        return this.joinKeyValue([key, value]).toLowerCase().replace(/\s/g, '')
      })
      return this.joinPair(arr)
    },
  }
}

export const extractNumbersFromPhoneNumber = (str: string) => {
  return str?.replace(/\D/g, '')
}

export const removeSpecialCharacters = (str?: string) => {
  return str?.replace(/[^a-zA-Z0-9]/g, '')
}

export const getFirstNCharacters = (str: string | undefined | null, length: number) => {
  return str?.substring(0, length)
}

export const addLeadingZero = (number: number | undefined, length: number) => {
  return number?.toString()?.padStart(length, '0')
}

export const validateJson = (json: string) => {
  try {
    JSON.parse(json)
    return true
  } catch (e) {
    return false
  }
}

export class Encoding {
  static encodeBase64 = (plaintText: string): string => {
    return Buffer.from(plaintText).toString('base64')
  }

  private static decodeBase64 = (encodedText: string): string => {
    return Buffer.from(encodedText, 'base64').toString()
  }

  static encodePrimaryKeys = (primaryKeys: IPrimarykeys) => {
    return Encoding.encodeBase64(JSON.stringify({ pk: primaryKeys.pk, sk: primaryKeys.sk }))
  }

  static decodePrimaryKeys = (encodedPrimaryKeys: string): IPrimarykeys => {
    return JSON.parse(Encoding.decodeBase64(encodedPrimaryKeys))
  }
}

export function getPipelineConfigStr({
  tableArn = 'dummy-arn',
  bucketName = 'dummy-bucket',
  collectionEndpoint = 'dummy-endpoint',
  networkPolicyName = 'dummy-policy',
  pipelineRoleArn = 'dummy-role',
}: {
  tableArn: string
  bucketName: string
  collectionEndpoint: string
  networkPolicyName: string
  pipelineRoleArn: string
}) {
  return `version: "2"
dynamodb-pipeline:
  source:
    dynamodb:
      tables:
        - table_arn: "${tableArn}"
          stream:
            start_position: "LATEST"
          export:
            s3_bucket: "${bucketName}"
            s3_region: "us-east-1"
            s3_prefix: "export/"
      aws:
        sts_role_arn: "${pipelineRoleArn}"
        region: "us-east-1"
  route:
    - product: 'contains(/pk, "P#")'
    - category: 'contains(/pk, "C#")'
  sink:
    - opensearch:
        hosts: [ "${collectionEndpoint}" ]
        index: "product"
        routes: [ "product" ]
        document_id: "\${getMetadata(\\"partition_key\\")}"
        actions:
          - type: "delete"
            when: 'getMetadata("opensearch_action") == "delete"'
          - type: "upsert"
            when: 'getMetadata("opensearch_action") != "delete"'
        aws:
          sts_role_arn: "${pipelineRoleArn}"
          region: "us-east-1"
          serverless: true
          serverless_options:
            network_policy_name: "${networkPolicyName}"
        dlq:
          s3:
            bucket: "${bucketName}"
            key_path_prefix: "dlq/product"
            region: "us-east-1"
            sts_role_arn: "${pipelineRoleArn}"
    - opensearch:
        hosts: [ "${collectionEndpoint}" ]
        index: "category"
        routes: [ "category" ]
        document_id: "\${getMetadata(\\"partition_key\\")}"
        action: "\${getMetadata(\\"opensearch_action\\")}"
        aws:
          sts_role_arn: "${pipelineRoleArn}"
          region: "us-east-1"
          serverless: true
          serverless_options:
            network_policy_name: "${networkPolicyName}"
        dlq:
          s3:
            bucket: "${bucketName}"
            key_path_prefix: "dlq/category"
            region: "us-east-1"
            sts_role_arn: "${pipelineRoleArn}"`
}
