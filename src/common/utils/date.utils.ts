import { Dayjs } from 'dayjs'
import { dayjs } from '../dayjs/dayjs'
import { Timezone } from '../../models/common/enums'

class DateUtils {
  private FORMAT = {
    DATE: 'YYYYMMDD',
    TIME: 'HHmmss',
    ISO: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
  }

  private UTC_SUFFIX = {
    NUMERIC: '+00:00',
    Z: 'Z',
  }

  convertUTCToTz(date: string, timezone?: string) {
    const day = dayjs(date)
    if (timezone) return day.tz(timezone)
    return dayjs(date).utc()
  }

  getNow(timezone?: string) {
    const nowUTC = dayjs().utc()

    return timezone
      ? nowUTC.tz(timezone)
      : nowUTC
  }

  /**
   * e.g. 
   * state: Ohio timezone: America/New_York (4h before UTC)
   * 
   * lambda schedule is triggered at 1am in America/New_York
   * now (lambda schedule runtime):                         2024-08-22T01:10:00.000 (America/New_York) => 2024-08-22T05:10:00.000Z (UTC)
   * startOfToday:                                          2024-08-22T00:00:00.000 (America/New_York) => 2024-08-22T04:00:00.000Z (UTC)
   * startOfYesterday:                                      2024-08-21T00:00:00.000 (America/New_York) => 2024-08-21T04:00:00.000Z (UTC)
   * merchants whose last sale submitted: < (not inclusive) 2024-08-21T00:00:00.000 (America/New_York) or 2024-08-21T04:00:00.000Z (UTC) are subject to zero report
   */
  getStartOfYesterday(timezone: Timezone) {
    const minusOneDay = this.getNow(timezone).subtract(1, 'day')
    return minusOneDay.startOf('day').utc()
  }

  getZeroReportEndDatetime(timezone: string) { 
    const minusOneDay = this.getNow(timezone).subtract(1, 'day')
    return minusOneDay.endOf('day').utc()
  }

  shouldSubmitZeroReport(saleSubmittedAt: string, thresholdDatetime: string) {
    return dateUtils.isBefore(saleSubmittedAt, thresholdDatetime)
  }

  isAfter(targetDt: string, compareDt: string) {
    return dayjs(targetDt).isAfter(dayjs(compareDt))
  }

  isBefore(targetDt: string, compareDt: string) {
    return dayjs(targetDt).isBefore(dayjs(compareDt))
  }

  isSame(targetDt: string, compareDt: string) {
    return dayjs(targetDt).isSame(dayjs(compareDt))
  }

  format(datetime: Dayjs, type: 'date' | 'time' | 'iso') {
    switch (type) {
      case 'date':
        return datetime.format(this.FORMAT.DATE)
      case 'time':
        return datetime.format(this.FORMAT.TIME)
      case 'iso':
        const iso = datetime.format(this.FORMAT.ISO)
        return iso.endsWith(this.UTC_SUFFIX.NUMERIC)
          ? iso.replace(this.UTC_SUFFIX.NUMERIC, this.UTC_SUFFIX.Z)
          : iso
    }
  }
}

export const dateUtils = new DateUtils()
