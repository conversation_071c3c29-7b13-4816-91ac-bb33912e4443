import { MerchantIdPathParameter } from '../dto/common'
import { InfoType, RecordType } from '../models/common/enums'
import { UpdateProductPriceReq } from '../dto/product-price'
import { IProductPriceEntity } from '../models/schemas/ProductPrice'
import { generateProductPricePk } from '../utils/product-price'

export class ProductPriceConverter {
  static toEntityCreate(
    pathReq: MerchantIdPathParameter,
    req: UpdateProductPriceReq,
  ): Partial<IProductPriceEntity> {
    return {
      pk: generateProductPricePk(req.companyProductId),
      sk: InfoType.PRICE,
      tenantId: pathReq.tenantId,
      recordType: RecordType.PRODUCT_PRICE,
      ...req,
    }
  }

  static toEntityUpdate(req: UpdateProductPriceReq): Partial<IProductPriceEntity> {
    return req
  }
}
