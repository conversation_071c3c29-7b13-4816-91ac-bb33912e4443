import { uuidv7 } from '@kripod/uuidv7'
import { CreateChildCategoryReq, UpdateChildCategoryReq, CreateCategoryReq, UpdateCategoryReq } from '../dto/category'
import { MerchantIdPathParameter } from '../dto/common'
import { ICategoryEntity, IChildCategoryEntity } from '../models/schemas/Category'
import { generateCategoryPk } from '../utils/category'

export class CategoryConverter {
  static toEntityCreate(
    pathReq: MerchantIdPathParameter,
    req: CreateCategoryReq,
  ): Omit<ICategoryEntity, 'createdAt' | 'updatedAt' | 'deletedAt' | 'recordType'> {
    const id = uuidv7()
    return {
      pk: generateCategoryPk(pathReq.pos, pathReq.tenantId),
      sk: id,
      tenantId: pathReq.tenantId,
      pos: pathReq.pos,
      ...req,
      id,
    }
  }

  static toChildEntityCreate(
    pathReq: MerchantIdPathParameter,
    parentReq: CreateCategoryReq,
    req: CreateChildCategoryReq,
  ): Partial<IChildCategoryEntity> {
    const id = uuidv7()
    return {
      pk: generateCategoryPk(pathReq.pos, pathReq.tenantId),
      sk: `${req.parentCategoryId}#${id}`,
      tenantId: pathReq.tenantId,
      pos: pathReq.pos,
      ...req,
      cannabisTypeId: parentReq.cannabisTypeId,
      cannabisTypeName: parentReq.cannabisTypeName,
      measurementType: parentReq.measurementType,
      isActive: parentReq.isActive,
      id,
    }
  }

  static toEntityUpdate(req: UpdateCategoryReq): Partial<ICategoryEntity> {
    return req
  }

  static toChildEntityUpdate(parentReq: UpdateCategoryReq, req: UpdateChildCategoryReq): Partial<IChildCategoryEntity> {
    return {
      ...req,
      cannabisTypeId: parentReq.cannabisTypeId,
      cannabisTypeName: parentReq.cannabisTypeName,
      measurementType: parentReq.measurementType,
      isActive: parentReq.isActive,
    }
  }
}