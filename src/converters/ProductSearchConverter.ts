import { SearchProductReq, SearchProductRes, sortFieldMappings } from '../dto/product-search'
import {
  QueryDslQueryContainer,
  SearchRequest,
  SearchResponse,
} from '@opensearch-project/opensearch/api/types'
import { OpensearchIndex } from '../models/common/enums'
import { RequestParams } from '@opensearch-project/opensearch'
import { ProductSearchRes } from '../dto/product'
import { CommonConverter } from './CommonConverter'
import { MerchantIdPathParameter } from '../dto/common'

export class ProductSearchConverter {
  static toSearchRequest(
    pathReq: MerchantIdPathParameter,
    filter: SearchProductReq,
  ): RequestParams.Search {
    const must: QueryDslQueryContainer | QueryDslQueryContainer[] = []
    const should: QueryDslQueryContainer | QueryDslQueryContainer[] = []

    let osfilterMustClause: QueryDslQueryContainer | QueryDslQueryContainer[] = [
      { term: { tenantId: pathReq.tenantId } }
    ]
    

    if (filter.query) {
      const lowerCaseQuery = filter.query.toLowerCase()
      should.push(
        {
          wildcard: {
            'retailerOverrides.sku': {
              value: `${lowerCaseQuery}*`,
              boost: 3.0,
            },
          },
        },
        {
          match_phrase: {
            name: {
              query: `${lowerCaseQuery}`,
              boost: 5.0
            }
          },
        },
      )
    }

    if (filter.categoryIds && filter.categoryIds.length > 0) {
      const lowerCaseCategoryIds = filter.categoryIds.map((categoryId) => categoryId.toLowerCase())
      osfilterMustClause.push({ terms: { 'categoryId': lowerCaseCategoryIds } })
    }

    if (filter.brandIds && filter.brandIds.length > 0) {
      const lowerCaseBrandIds = filter.brandIds.map((brandId) => brandId.toLowerCase())
      osfilterMustClause.push({ terms: { 'brandId': lowerCaseBrandIds } })
    }

    if (filter.active !== undefined) {
      osfilterMustClause.push({ term: { isActive: filter.active } })
    }

    if (filter.tags && filter.tags.length > 0) {
      const lowerCaseTags = filter.tags.map((tag) => tag.toLowerCase())
      osfilterMustClause.push({ terms: { 'tags': lowerCaseTags } })
    }

    if (filter.minPrice || filter.maxPrice) {
      must.push({
        range: {
          unitPrice: {
            gte: filter.minPrice,
            lte: filter.maxPrice,
          },
        },
      })
    }

    if (filter.isDeleted) {
        osfilterMustClause.push({ exists: { field: 'deletedAt' } })
    } else {
      osfilterMustClause.push({bool: { must_not: { exists: { field: 'deletedAt' } }}})
    }

    if (filter.vendorIds && filter.vendorIds.length > 0) {
      const lowerCaseVendorIds = filter.vendorIds.map((vendorId) => vendorId.toLowerCase())
      osfilterMustClause.push({ terms: { 'vendorIds': lowerCaseVendorIds } })
    }

    const searchRequest: SearchRequest = {
      index: OpensearchIndex.PRODUCT,
      body: {
        size: filter.size,
        query: {
          bool: {
            must,
            should,
            minimum_should_match: should.length === 0 ? 0 : 1,
            filter: {
              bool: {
                must: osfilterMustClause
              }
            },
          },
        },
        sort: [{ [sortFieldMappings[filter.sortBy]]: filter.sortDirection }, { _id: 'asc' }],
        ...(filter.searchAfter && { search_after: filter.searchAfter }),
      },
    }

    return searchRequest as RequestParams.Search
  }

  static toSearchResponse(response: SearchResponse): SearchProductRes {
    const hits = response.hits.hits

    const products = hits.map((hit) => CommonConverter.toDto(hit._source || {}, ProductSearchRes))

    const searchAfter = hits.length ? hits[hits.length - 1].sort : undefined

    const count =
      typeof response.hits.total === 'object' ? response.hits.total.value : response.hits.total

    return {
      products,
      searchAfter,
      count,
    }
  }
}
