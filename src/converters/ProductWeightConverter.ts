import { IProductWeight, ProductWeight } from '../dto/product-weight'
import { ProductWeightType } from '../models/common/enums'

export class ProductWeightConverter {
  static makeProductSize(weight: number): IProductWeight {
    let display_text: string | null = null
    let units = 'grams'
    let type: ProductWeightType
    switch (weight) {
      case 1.0:
        type = ProductWeightType.FULL_GRAM
        display_text = '1 gram'
        break
      case 0.5:
        type = ProductWeightType.HALF_GRAM
        display_text = '1/2 gram'
        break
      case 0.125:
        type = ProductWeightType.EIGHTH
        units = 'ounces'
        display_text = '1/8 oz'
        break
      default:
        type = ProductWeightType.CUSTOM_GRAMS
        display_text = `${weight} grams`
    }
    return new ProductWeight({
      display_text,
      amount: weight,
      units,
      type,
    })
  }
}
