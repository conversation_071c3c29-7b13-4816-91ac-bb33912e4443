import { plainToInstance } from 'class-transformer'
import { validateClass } from '../common/error'
import Serializer from '../serializers'
import { ResourceType } from '../serializers/enums'
import { EventDetailType } from '../models/events/enums'
import { IEventPayload } from '../models/events/interfaces'

export class CommonConverter {
  static toDto<T extends object>(source: any, dtoClass: new () => T): T {
    const dtoInstance = plainToInstance(dtoClass, source, {
      excludeExtraneousValues: true,
      exposeDefaultValues: true,
    })

    validateClass(dtoInstance)

    return dtoInstance
  }

  static eventBodyToDto<T extends object>(
    eventBody: string | null,
    dtoClass: new () => T,
    resourceType: ResourceType,
  ): T {
    const parsedBody = JSON.parse(eventBody || '{}')
    const deserialized = Serializer.deserialize(resourceType, parsedBody)
    return this.toDto(deserialized, dtoClass)
  }

  static toEvent<T>(
    resource: T,
    resourceType: ResourceType,
    eventDetailType: EventDetailType,
  ): IEventPayload<unknown> {
    const serializedResource = Serializer.serialize(resourceType, resource)

    const eventPayload = {
      jsonDumps: serializedResource,
      eventDetailType,
    }

    return eventPayload
  }
}
