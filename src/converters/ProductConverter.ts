import { uuidv7 } from '@kripod/uuidv7'
import { MerchantIdPathParameter } from '../dto/common'
import { UpdateProductReq } from '../dto/product'
import { IProductEntity } from '../models/schemas/Product'
import { generateProductPk } from '../utils/product'
import { InfoType, RecordType } from '../models/common/enums'

export class ProductConverter {
  static toEntityCreate(
    pathReq: MerchantIdPathParameter,
    req: UpdateProductReq,
  ): Partial<IProductEntity> {
    const id = uuidv7()
    return {
      pk: generateProductPk(id),
      sk: InfoType.INFO,
      id,
      tenantId: pathReq.tenantId,
      pos: pathReq.pos,
      recordType: RecordType.PRODUCT,
      ...req,
    }
  }

  static toEntityUpdate(req: UpdateProductReq): Partial<IProductEntity> {
    return req
  }
}
