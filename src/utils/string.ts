import { createHash } from 'crypto'

export const canonicalize = (str: string) => {
  return str
    .trim() // 1. strip outer whitespace
    .toLowerCase() // 2. make case‑insensitive
    .normalize('NFD') // 3. split letters + accents
    .replace(/\p{Diacritic}/gu, '') // 4. drop the accents
    .replace(/[^a-z0-9]/g, '') // 5. keep only alphanumeric characters
}

export const createMD5Hash = (str: string) => {
  return createHash('md5').update(str).digest('hex')
}

export const toBooleanString = (value: boolean): 'TRUE' | 'FALSE' => {
  return value ? 'TRUE' : 'FALSE'
}
