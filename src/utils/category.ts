import { generateKey } from '../common/db/dynamoose/utils'
import { RecordType } from '../models/common/enums'

/**
 * Generates a partition key for a category based on merchant details.
 *
 * @param pos - The POS identifier (Point of Sale)
 * @param tenantId - The tenant identifier
 * @returns The generated partition key for the category
 */
export function generateCategoryPk(pos: string, tenantId: string): string {
  return generateKey(RecordType.POS, pos, RecordType.TENANT, tenantId, RecordType.CATEGORY)
}

/**
 * Extracts the tenant ID from a category partition key.
 *
 * @param pk - The partition key to extract from.
 * @returns The extracted tenant ID or undefined if not found.
 */
export function getTenantIdFromPk(pk: string): string | undefined {
  const parts = pk.split('#')

  const tenantIndex = parts.findIndex((part) => part === RecordType.TENANT)

  return tenantIndex !== -1 && parts.length > tenantIndex + 1 ? parts[tenantIndex + 1] : undefined
}
