import { S3Client, PutObjectCommandInput } from '@aws-sdk/client-s3'
import { Upload } from '@aws-sdk/lib-storage'
import { FormatterOptionsArgs, format as formatCSV } from '@fast-csv/format'
import { ParserOptionsArgs, parseString } from '@fast-csv/parse'
import { PassThrough } from 'stream'
import { TCreateBlazeCanadaProductCSVRow } from '../models/apis/blaze-canada/Product'

type CsvRow = Record<string, string>
type TransformedCsvRow = Record<string, string | undefined>

export const parseCsv = (
  csv: string,
  options?: ParserOptionsArgs,
): Promise<Record<string, string>[]> =>
  new Promise((resolve, reject) => {
    const rows: Record<string, string>[] = []

    parseString<CsvRow, TransformedCsvRow>(csv, options)
      .transform((row: CsvRow): TransformedCsvRow => {
        const transformedRow: TransformedCsvRow = {}
        Object.entries(row).forEach(([key, value]) => {
          transformedRow[key] = value === '' ? undefined : value
        })

        return transformedRow
      })
      .on('error', reject)
      .on('data', (row) => rows.push(row))
      .on('end', () => resolve(rows))
  })

export const uploadCsvToS3 = async <T extends object>(
  records: T[],
  {
    bucket,
    key,
    csvOptions,
    s3Client = new S3Client(),
  }: {
    bucket: string
    key: string
    csvOptions?: FormatterOptionsArgs<T, T>
    s3Client?: S3Client
  },
) => {
  const body = new PassThrough()

  const uploader = new Upload({
    client: s3Client,
    params: {
      Bucket: bucket,
      Key: key,
      Body: body,
      ContentType: 'text/csv',
    } satisfies PutObjectCommandInput,
  })

  const csvStream = formatCSV<T, T>({
    headers: true,
    ...csvOptions,
  })

  csvStream.pipe(body)

  for (const row of records) csvStream.write(row)
  csvStream.end()

  const { Key, Bucket } = await uploader.done()

  if (!Key || !Bucket) throw new Error('Failed to upload CSV to S3')

  return {
    bucket: Bucket,
    key: Key,
  }
}
