import { Expose } from 'class-transformer'
import { IsNotEmpty, IsString, Validate } from 'class-validator'
import { CustomMerchantIdValidator } from '../common/validators/CustomMerchantIdValidator'

export class MerchantIdPathParameter {
  @Expose()
  @IsString()
  @Validate(CustomMerchantIdValidator)
  @IsNotEmpty()
  merchantId: string

  get pos(): string {
    return this.merchantId.split('-')[0]
  }

  get tenantId(): string {
    return this.merchantId.split('-')[1]
  }

  get retailerId(): string {
    return this.merchantId.split('-')[2]
  }
}
