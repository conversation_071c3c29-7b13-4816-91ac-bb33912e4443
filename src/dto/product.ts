import { Expose } from 'class-transformer'

import {
  IsArray,
  IsBoolean,
  IsDefined,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator'
import { ProductType } from '../models/common/enums'
import { MerchantIdPathParameter } from './common'
import { IProductAttributes, IProductRetailerOverrides, IProductSearchContract } from '../models/schemas/Product'
import { IProductRetailerPriceAttributes } from '../models/schemas/ProductPrice'

export class UpdateProductReq implements IProductAttributes {
  @Expose()
  @IsString()
  @IsNotEmpty()
  name: string

  @Expose()
  @IsString()
  @IsOptional()
  description: string

  @Expose()
  @IsString()
  @IsOptional()
  flowerType: string

  @Expose()
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  tags: string[] = []

  @Expose()
  @IsString()
  @IsOptional()
  categoryId: string

  @Expose()
  @IsString()
  @IsOptional()
  brandId: string

  @IsBoolean()
  @IsOptional()
  @Expose()
  isAvailableOnline: boolean = false

  @Expose()
  @IsNotEmpty()
  @IsEnum(ProductType)
  type: ProductType

  @IsBoolean()
  @IsOptional()
  @Expose()
  isActive: boolean = false

  @Expose()
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  images: string[] = []

  @Expose()
  @IsNumber()
  @IsOptional()
  cannabisWeight: number

  @Expose()
  @IsString()
  @IsOptional()
  cannabisWeightUnit: string

  @Expose()
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  vendorIds: string[] = []

  @Expose()
  @IsString()
  @IsOptional()
  sku: string

  @Expose()
  @IsNumber()
  @IsOptional()
  liThreshold: number

  @Expose()
  @IsArray()
  @IsOptional()
  @IsObject({ each: true })
  retailerOverrides: IProductRetailerOverrides[] = []
}

export class ProductRes implements IProductAttributes {
  @Expose()
  id: string

  @Expose()
  tenantId: string

  @Expose()
  pos: string

  @Expose()
  updatedAt: string

  @Expose()
  createdAt: string

  @Expose()
  deletedAt: string

  @Expose()
  name: string

  @Expose()
  description: string

  @Expose()
  flowerType: string

  @Expose()
  tags: string[]

  @Expose()
  categoryId: string

  @Expose()
  brandId: string

  @Expose()
  isAvailableOnline: boolean

  @Expose()
  type: ProductType
  
  @Expose()
  isActive: boolean
  
  @Expose()
  images: string[]

  @Expose()
  cannabisWeight: number

  @Expose()
  cannabisWeightUnit: string

  @Expose()
  vendorIds: string[]

  @Expose()
  sku: string

  @Expose()
  liThreshold: number

  @Expose()
  retailerOverrides: IProductRetailerOverrides[] = []
}

export class ProductSearchRes extends ProductRes implements IProductSearchContract {
  @Expose()
  unitPrice: number

  @Expose()
  retailerPrices: IProductRetailerPriceAttributes[]
}

export class ProductIdPathReq extends MerchantIdPathParameter {
  @Expose()
  @IsString()
  @IsNotEmpty()
  productId: string
}
