import { Expose, Type } from 'class-transformer'
import { IsArray, IsBoolean, IsDefined, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator'
import { MerchantIdPathParameter } from './common'
import { EMeasurementType, ICategoryContract, IChildCategoryContract } from '../models/schemas/Category'

export class CreateChildCategoryReq implements IChildCategoryContract {
  @IsString()
  @Expose()
  @IsOptional()
  id: string

  @Expose()
  @IsString()
  @IsNotEmpty()
  name: string

  @Expose()
  @IsString()
  @IsOptional()
  parentCategoryId: string

  @IsBoolean()
  @IsOptional()
  @Expose()
  isActive: boolean = true

  @IsString()
  @Expose()
  @IsOptional()
  deletedAt: string

  @IsString()
  @Expose()
  @IsOptional()
  measurementType: EMeasurementType = EMeasurementType.UNTIS

  @IsString()
  @Expose()
  @IsOptional()
  cannabisTypeId: string

  @IsString()
  @Expose()
  @IsOptional()
  cannabisTypeName: string

  @IsString()
  @Expose()
  @IsOptional()
  exitLabel: string | null

  @IsString()
  @Expose()
  @IsOptional()
  image: string
}

export class UpdateChildCategoryReq implements IChildCategoryContract {
  @IsString()
  @Expose()
  @IsOptional()
  id: string

  @Expose()
  @IsString()
  @IsNotEmpty()
  name: string

  @Expose()
  @IsString()
  @IsNotEmpty()
  parentCategoryId: string

  @IsBoolean()
  @IsOptional()
  @Expose()
  isActive: boolean = true

  @IsString()
  @Expose()
  @IsOptional()
  deletedAt: string

  @IsString()
  @Expose()
  @IsOptional()
  measurementType: EMeasurementType = EMeasurementType.UNTIS

  @IsString()
  @Expose()
  @IsOptional()
  cannabisTypeId: string

  @IsString()
  @Expose()
  @IsOptional()
  cannabisTypeName: string

  @IsString()
  @Expose()
  @IsOptional()
  exitLabel: string | null

  @IsString()
  @Expose()
  @IsOptional()
  image: string
}

export class UpdateCategoryReq implements ICategoryContract {
  @IsString()
  @Expose()
  @IsNotEmpty()
  id: string

  @Expose()
  @IsString()
  @IsNotEmpty()
  name: string

  @Expose()
  @IsString()
  @IsOptional()
  parentCategoryId: string | null

  @IsBoolean()
  @IsOptional()
  @Expose()
  isActive: boolean = true

  @IsString()
  @Expose()
  @IsOptional()
  deletedAt: string

  @IsString()
  @Expose()
  @IsOptional()
  measurementType: EMeasurementType = EMeasurementType.UNTIS

  @IsString()
  @Expose()
  @IsNotEmpty()
  cannabisTypeId: string

  @IsString()
  @Expose()
  @IsNotEmpty()
  cannabisTypeName: string

  @IsString()
  @Expose()
  @IsOptional()
  exitLabel: string | null

  @IsString()
  @Expose()
  @IsNotEmpty()
  image: string

  @IsArray()
  @IsObject({ each: true })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => UpdateChildCategoryReq)
  @Expose()
  childCategories: UpdateChildCategoryReq[] = []

  @IsNumber()
  @Expose()
  @IsNotEmpty()
  orderIndex: number
}

export class CreateCategoryReq implements ICategoryContract {
  @IsString()
  @Expose()
  @IsOptional()
  id: string

  @Expose()
  @IsString()
  @IsNotEmpty()
  name: string

  @Expose()
  @IsString()
  @IsOptional()
  parentCategoryId: string | null

  @IsBoolean()
  @IsOptional()
  @Expose()
  isActive: boolean = true

  @IsString()
  @Expose()
  @IsOptional()
  deletedAt: string

  @IsString()
  @Expose()
  @IsOptional()
  measurementType: EMeasurementType = EMeasurementType.UNTIS

  @IsString()
  @Expose()
  @IsNotEmpty()
  cannabisTypeId: string

  @IsString()
  @Expose()
  @IsNotEmpty()
  cannabisTypeName: string

  @IsString()
  @Expose()
  @IsOptional()
  exitLabel: string | null

  @IsString()
  @Expose()
  @IsNotEmpty()
  image: string

  @IsArray()
  @IsObject({ each: true })
  @IsOptional()
  @Type(() => CreateChildCategoryReq)
  @ValidateNested({ each: true })
  @Expose()
  childCategories: CreateChildCategoryReq[] = []

  @IsNumber()
  @Expose()
  @IsNotEmpty()
  orderIndex: number
}

export class CategoryRes implements ICategoryContract {
  @Expose()
  id: string

  @Expose()
  tenantId: string

  @Expose()
  pos: string

  @Expose()
  name: string

  @Expose()
  parentCategoryId: string | null

  @Expose()
  isActive: boolean

  @Expose()
  deletedAt?: string

  @Expose()
  updatedAt: string

  @Expose()
  createdAt: string

  @Expose()
  measurementType: EMeasurementType

  @Expose()
  cannabisTypeId: string

  @Expose()
  cannabisTypeName: string

  @Expose()
  exitLabel: string | null

  @Expose()
  image: string

  @Expose()
  orderIndex: number

  @Expose()
  childCategories: ChildCategoryRes[]
}

export class ChildCategoryRes implements IChildCategoryContract {
  @Expose()
  id: string

  @Expose()
  tenantId: string

  @Expose()
  pos: string

  @Expose()
  name: string

  @Expose()
  parentCategoryId: string

  @Expose()
  isActive: boolean

  @Expose()
  deletedAt?: string

  @Expose()
  updatedAt: string

  @Expose()
  createdAt: string

  @Expose()
  measurementType: EMeasurementType

  @Expose()
  cannabisTypeId: string

  @Expose()
  cannabisTypeName: string

  @Expose()
  exitLabel: string | null

  @Expose()
  image: string
}

export class CategoryIdPathReq extends MerchantIdPathParameter {
  @Expose()
  @IsString()
  @IsNotEmpty()
  categoryId: string
}