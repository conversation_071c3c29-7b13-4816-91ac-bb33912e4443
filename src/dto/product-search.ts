import { Expose } from 'class-transformer'
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsNumber,
  IsArray,
  ArrayNotEmpty,
  Max,
  Min,
  IsEnum,
} from 'class-validator'
import { ProductSearchRes } from './product'

export enum SortByField {
  CREATED_AT = 'createdAt',
  NAME = 'name',
  UNIT_PRICE = 'unitPrice',
}

export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc',
}

export const sortFieldMappings: Record<SortByField, string> = {
  [SortByField.CREATED_AT]: SortByField.CREATED_AT,
  [SortByField.NAME]: `${SortByField.NAME}.keyword`,
  [SortByField.UNIT_PRICE]: SortByField.UNIT_PRICE,
}

export class SearchProductReq {
  @Expose()
  @IsString()
  @IsOptional()
  query?: string

  @Expose()
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  categoryIds?: string[]

  @Expose()
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  brandIds?: string[]

  @Expose()
  @IsBoolean()
  @IsOptional()
  active?: boolean

  @Expose()
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  tags?: string[]

  @Expose()
  @IsNumber()
  @IsOptional()
  minPrice?: number

  @Expose()
  @IsNumber()
  @IsOptional()
  maxPrice?: number

  @Expose()
  @Min(1)
  @Max(500)
  size: number

  @Expose()
  @IsEnum(SortByField)
  sortBy: SortByField

  @Expose()
  @IsEnum(SortDirection)
  sortDirection: SortDirection

  @Expose()
  @IsArray()
  @ArrayNotEmpty()
  @IsOptional()
  searchAfter?: Array<any>

  @Expose()
  @IsBoolean()
  @IsOptional()
  isDeleted?: boolean

  @Expose()
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  vendorIds?: string[]
}

export class SearchProductRes {
  @Expose()
  products: ProductSearchRes[]

  @Expose()
  searchAfter?: Array<any>

  @Expose()
  count: number
}
