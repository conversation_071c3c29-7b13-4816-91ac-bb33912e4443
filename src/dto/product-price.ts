import { Expose } from "class-transformer"
import { IsString, IsNotEmpty, IsOptional, IsNumber, IsArray, IsObject } from "class-validator"
import { IProductPriceAttributes, IProductRetailerPriceAttributes } from "../models/schemas/ProductPrice"
import { MerchantIdPathParameter } from "./common"

export class ProductPriceRes implements IProductPriceAttributes {
  @Expose()
  pk: string

  @Expose()
  sk: string

  @Expose()
  tenantId: string

  @Expose()
  unitPrice: number

  @Expose()
  companyProductId: string

  @Expose()
  retailerPrices: IProductRetailerPriceAttributes[]

  @Expose()
  recordType: string

  @Expose()
  deletedAt: string

  @Expose()
  createdAt: string

  @Expose()
  updatedAt: string
}

export class UpdateProductPriceReq implements IProductPriceAttributes {
  @Expose()
  @IsNumber()
  @IsNotEmpty()
  unitPrice: number

  @Expose()
  @IsString()
  @IsNotEmpty()
  companyProductId: string

  @Expose()
  @IsArray()
  @IsOptional()
  @IsObject({ each: true })
  retailerPrices: IProductRetailerPriceAttributes[] = []
}


export class ProductPriceIdPathReq extends MerchantIdPathParameter {
  @Expose()
  @IsString()
  @IsNotEmpty()
  productId: string
}