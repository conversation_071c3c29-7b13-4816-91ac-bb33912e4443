import { ProductWeightType } from '../models/common/enums'

export interface IProductWeight {
  display_text: string
  amount: number
  units: string
  type: ProductWeightType
}

export class ProductWeight implements IProductWeight {
  display_text: string
  amount: number
  units: string
  type: ProductWeightType

  constructor({ display_text, amount, units, type }: IProductWeight) {
    this.display_text = display_text
    this.amount = amount
    this.units = units
    this.type = type
  }
}
