import { Expose } from 'class-transformer'
import { IsOptional, IsNumber } from 'class-validator'
import { IPotency } from '../models/schemas/Potency'

export class Potency implements IPotency {
  @Expose()
  @IsOptional()
  @IsNumber()
  thc?: number

  @Expose()
  @IsOptional()
  @IsNumber()
  cbd?: number

  @Expose()
  @IsOptional()
  @IsNumber()
  cbn?: number

  @Expose()
  @IsOptional()
  @IsNumber()
  cbda?: number

  @Expose()
  @IsOptional()
  @IsNumber()
  thca?: number

  @Expose()
  @IsOptional()
  @IsNumber()
  min_thc?: number

  @Expose()
  @IsOptional()
  @IsNumber()
  max_thc?: number

  @Expose()
  @IsOptional()
  @IsNumber()
  min_cbd?: number

  @Expose()
  @IsOptional()
  @IsNumber()
  max_cbd?: number
}
