import { CategoryIdPathReq, CreateCategoryReq, UpdateCategoryReq } from '../dto/category'
import { MerchantIdPathParameter } from '../dto/common'
import { AwsEventPublisher } from '../events/utilities'
import { EventDetailType } from '../models/events/enums'
import { ResourceType } from '../serializers/enums'
import { CategoryService } from '../services/CategoryService'

export class CategoryAction {
  static async createCategory(pathReq: MerchantIdPathParameter, bodyReq: CreateCategoryReq) {
    return CategoryService.createCategory(pathReq, bodyReq)
  }

  static async updateCategory(pathReq: CategoryIdPathReq, bodyReq: UpdateCategoryReq) {
    return CategoryService.updateCategory(pathReq, bodyReq)
  }

  static async listCategories(req: MerchantIdPathParameter) {
    return await CategoryService.listCategories(req)
  }

  static async deleteCategory(pathReq: CategoryIdPathReq) {
    await CategoryService.deleteCategory(pathReq)
  }
}
