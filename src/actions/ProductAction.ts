import { MerchantIdPathParameter } from '../dto/common'
import { ProductIdPathReq, UpdateProductReq } from '../dto/product'
import { AwsEventPublisher } from '../events/utilities'
import { EventDetailType } from '../models/events/enums'
import { ResourceType } from '../serializers/enums'
import { ProductService } from '../services/ProductService'

export class ProductAction {
  static async createProduct(pathReq: MerchantIdPathParameter, bodyReq: UpdateProductReq) {
    const product = await ProductService.createProduct(pathReq, bodyReq)

    await AwsEventPublisher.publish(
      product,
      ResourceType.PRODUCT_EVENT,
      EventDetailType.PRODUCT_CREATED,
    )
    return product
  }

  static async updateProduct(id: string, req: UpdateProductReq) {
    const product = await ProductService.updateProduct(id, req)

    await AwsEventPublisher.publish(
      product,
      ResourceType.PRODUCT_EVENT,
      EventDetailType.PRODUCT_UPDATED,
    )
    await AwsEventPublisher.publish(
      {
        id: product.id,
        tenantId: product.tenantId,
        liThreshold: product.liThreshold,
        retailerOverrides: product.retailerOverrides.map((ro) => (
          {
            retailerId: ro.retailerId,
            liThreshold: ro.liThreshold,
          }
        )),
      },
      ResourceType.INVENTORY_INFO,
      EventDetailType.INVENTORY_INFO_UPDATED,
    )
    return product
  }

  static async deleteProduct(pathReq: ProductIdPathReq) {
    await ProductService.deleteProduct(pathReq.productId)

    await AwsEventPublisher.publish(
      { id: pathReq.productId, tenantId: pathReq.tenantId, pos: pathReq.pos },
      ResourceType.PRODUCT_EVENT,
      EventDetailType.PRODUCT_DELETED,
    )
  }
}
