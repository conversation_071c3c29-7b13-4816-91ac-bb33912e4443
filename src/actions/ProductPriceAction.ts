import { MerchantIdPathParameter } from "../dto/common";
import { UpdateProductPriceReq, ProductPriceIdPathReq } from "../dto/product-price";
import { ProductPriceService } from "../services/ProductPriceService";
import { ProductService } from "../services/ProductService";
import { generateProductPk } from "../utils/product";

export class ProductPriceAction {
  static async createProductPrice(pathReq: MerchantIdPathParameter, req: UpdateProductPriceReq) {
    const pk = generateProductPk(req.companyProductId)
    await ProductService.checkProductExists(pk)
    return ProductPriceService.createProductPrice(pathReq, req)
  }
}
