import { BlazeCanadaAPI } from '../apis/BlazeCanadaAPI'
import { DomainAdapter } from '../models/adapters/DomainAdapter'
import { TCreateBlazeCanadaSupplier } from '../models/apis/blaze-canada/Supplier'
import { EPOSProvider } from '../models/common/enums'
import {
  TAOBrandContract,
  TCreateAOBrandContract,
} from '../models/contracts/auto-onboarding/Brand/BrandContract'
import {
  BrandExportRowFields,
  TBrandExportRow,
  TDedupedBrandExportRow,
  parseBrandExport,
} from '../models/contracts/auto-onboarding/Brand/BrandExportRow'
import { AOBrandService } from '../services/auto-onboarding/AOBrandService'
import {
  getMerchantIdFromCompanyId,
  groupedRecordsByKey,
  hashCanonicalStr,
  unifyRecordGroup,
} from './utils'

export class BrandAdapter
  implements DomainAdapter<TBrandExportRow, TAOBrandContract, never, false>
{
  private companyId: string
  private service: AOBrandService

  constructor(companyId: string) {
    this.companyId = companyId
    this.service = new AOBrandService(companyId)
  }

  private getIdFromExportRow(record: TBrandExportRow): string {
    return record.id
  }

  public isBulkDomain(): false {
    return false
  }

  public parse = parseBrandExport

  public getIdFromContract(contract: TAOBrandContract): string {
    return contract.id
  }

  public getDedupeKeyFromContract(contract: TAOBrandContract): string {
    return contract.key
  }

  public csvKey() {
    return `companies/${this.companyId}/brands/company-${this.companyId}-brands-export.csv`
  }

  public dedupeKey(record: TBrandExportRow): string {
    return hashCanonicalStr(record.name)
  }

  public unifyMatchingRecords(records: TBrandExportRow[]): TDedupedBrandExportRow[] {
    const recordGroupsMap = groupedRecordsByKey<TBrandExportRow>(records, this.dedupeKey)
    const unifiedRecords: TDedupedBrandExportRow[] = []
    const recordGroups = recordGroupsMap.entries()

    for (const [key, group] of recordGroups) {
      const unifiedRecord = unifyRecordGroup(key, group, {
        getId: this.getIdFromExportRow,
        fields: BrandExportRowFields,
      })
      unifiedRecords.push(unifiedRecord)
    }

    return unifiedRecords
  }

  public async saveRecords(records: TDedupedBrandExportRow[]): Promise<void> {
    const transformedRecords: TCreateAOBrandContract[] = records.map((record) => ({
      ...record,
      sourceMerchantId: getMerchantIdFromCompanyId(EPOSProvider.US, record.companyId),
    }))
    await this.service.bulkCreate(transformedRecords)
  }

  public async getSavedRecords(): Promise<TAOBrandContract[]> {
    return this.service.getAll()
  }

  public async createRecordInBlazeCanada(
    api: BlazeCanadaAPI,
    record: TAOBrandContract,
  ): Promise<string> {
    const blazeCanadaContract: TCreateBlazeCanadaSupplier = {
      companyId: parseInt(this.companyId),
      name: record.name,
      phone: record.phoneNumber,
      email: record.email,
      address: record.address,
      description: record.website,
    }

    const response = await api.createSupplier(this.companyId, blazeCanadaContract)
    return response.id.toString()
  }
}
