import { EPOSProvider } from '../models/common/enums'
import { TOverride, TDedupedRecord } from '../models/contracts/auto-onboarding/Overrides'
import { canonicalize, createMD5Hash } from '../utils/string'

export const hashCanonicalStr = (value: string): string => {
  const canonicalName = canonicalize(value)
  return createMD5Hash(canonicalName)
}

export const mergeRecords = <T extends object, U extends object>(
  defaults: T,
  overrides: U,
): T & U => {
  const result: object = { ...defaults }
  for (const [key, value] of Object.entries(overrides)) {
    if (result[key] === null || result[key] === undefined) {
      result[key] = value
    }
  }
  return result as T & U
}

export const mostFrequent = (arr: unknown[]): string | number | undefined | null => {
  const countByElement = {}
  let maxCount = 0
  let mostFrequentElement: string | number | undefined = undefined

  const definedElements = arr
    .filter((e) => typeof e === 'string' || typeof e === 'number')
    .map((e) => e as string | number)
  const undefinedCount = arr.filter((e) => e === undefined).length
  const nullCount = arr.filter((e) => e === null).length

  for (const element of definedElements) {
    countByElement[element] = (countByElement[element] ?? 0) + 1

    if (countByElement[element] > maxCount) {
      maxCount = countByElement[element]
      mostFrequentElement = element
    }
  }

  if (Math.max(undefinedCount, nullCount, maxCount) > maxCount) {
    return undefinedCount >= nullCount ? undefined : null
  }
  return mostFrequentElement
}

export const groupedRecordsByKey = <T>(
  records: T[],
  getDedupeKey: (record: T) => string,
): Map<string, T[]> => {
  const groupedRecords = new Map<string, T[]>()

  for (const record of records) {
    const key = getDedupeKey(record)
    const group = groupedRecords.get(key)
    group?.push(record) ?? groupedRecords.set(key, [record])
  }

  return groupedRecords
}

export const unifyRecordGroup = <T extends object, K extends keyof T & string>(
  dedupeKey: string,
  recordGroup: T[],
  options: {
    getId: (record: T) => string
    fields: (keyof T)[]
    overrideKey?: K
    overrideFields?: (keyof T)[]
  },
): TDedupedRecord<T, K> => {
  let unifiedRecord: TDedupedRecord<T, K> = {
    ...recordGroup[0],
    overrides: [],
    key: dedupeKey,
    specificRecordsIds: [],
  }

  // Apply base values for overridable fields
  for (const field of options.fields) {
    const baseValue = mostFrequent(recordGroup.map((record) => record[field]))
    unifiedRecord = {
      ...unifiedRecord,
      [field]: baseValue,
    }
  }

  for (const record of recordGroup) {
    unifiedRecord = {
      ...mergeRecords<TDedupedRecord<T, K>, T>(unifiedRecord, record),
      specificRecordsIds: [...unifiedRecord.specificRecordsIds, options.getId(record)],
    }

    // Apply overrides for fields that differ from the base value
    if (!options.overrideKey || !record[options.overrideKey]) continue
    for (const field of options.overrideFields ?? []) {
      unifiedRecord = {
        ...unifiedRecord,
        overrides: [
          ...unifiedRecord.overrides,
          ...(record[field] !== unifiedRecord[field]
            ? [
                {
                  [options.overrideKey]: record[options.overrideKey],
                  [field]: record[field],
                },
              ]
            : []),
        ] as TOverride<T, K>[],
      }
    }
  }

  return unifiedRecord
}

export const getMerchantIdFromCompanyId = <T extends EPOSProvider>(
  origin: T,
  companyId: string,
): `${T}-${string}` => {
  return `${origin}-${companyId}`
}
