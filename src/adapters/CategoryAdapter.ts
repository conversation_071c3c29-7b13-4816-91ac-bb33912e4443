import { BlazeCanadaAPI } from '../apis/BlazeCanadaAPI'
import { DomainAdapter } from '../models/adapters/DomainAdapter'
import {
  CategoryType,
  MeasurementType,
  TCreateBlazeCanadaCategory,
} from '../models/apis/blaze-canada/Category'
import { TAOCategoryContract } from '../models/contracts/auto-onboarding/Category/CategoryContract'
import {
  CategoryExportRowFields,
  TCategoryExportRow,
  TDedupedCategoryExportRow,
  parseCategoryExport,
} from '../models/contracts/auto-onboarding/Category/CategoryExportRow'
import { AOCategoryService } from '../services/auto-onboarding/AOCategoryService'
import { groupedRecordsByKey, hashCanonicalStr, unifyRecordGroup } from './utils'

export class CategoryAdapter
  implements DomainAdapter<TCategoryExportRow, TAOCategoryContract, 'shopId', false>
{
  private companyId: string
  private service: AOCategoryService

  constructor(companyId: string) {
    this.companyId = companyId
    this.service = new AOCategoryService(companyId)
  }

  private getIdFromExportRow(record: TCategoryExportRow): string {
    return record.id
  }

  private getBlazeCanadaMeasurementType(
    unitType: TCategoryExportRow['unitType'],
  ): TCreateBlazeCanadaCategory['measurementType'] {
    const measurementTypes: Record<
      TCategoryExportRow['unitType'],
      TCreateBlazeCanadaCategory['measurementType']
    > = {
      grams: MeasurementType.GRAMS,
      units: MeasurementType.UNIT,
    }
    return measurementTypes[unitType]
  }

  public isBulkDomain(): false {
    return false
  }

  public parse = parseCategoryExport

  public getIdFromContract(contract: TAOCategoryContract): string {
    return contract.id
  }

  public getDedupeKeyFromContract(contract: TAOCategoryContract): string {
    return contract.key
  }

  public csvKey() {
    return `companies/${this.companyId}/categories/company-${this.companyId}-categories-export.csv`
  }

  public dedupeKey(record: TCategoryExportRow): string {
    return hashCanonicalStr(record.name)
  }

  public unifyMatchingRecords(records: TCategoryExportRow[]): TDedupedCategoryExportRow[] {
    const recordGroupsMap = groupedRecordsByKey<TCategoryExportRow>(records, this.dedupeKey)
    const unifiedRecords: TDedupedCategoryExportRow[] = []
    const recordGroups = recordGroupsMap.entries()

    for (const [key, group] of recordGroups) {
      const unifiedRecord = unifyRecordGroup(key, group, {
        getId: this.getIdFromExportRow,
        fields: CategoryExportRowFields,
        overrideKey: 'shopId',
        overrideFields: ['lowInventoryThreshold'],
      })
      unifiedRecords.push(unifiedRecord)
    }

    return unifiedRecords
  }

  public async saveRecords(records: TDedupedCategoryExportRow[]): Promise<void> {
    await this.service.bulkCreate(records)
  }

  public async getSavedRecords(): Promise<TAOCategoryContract[]> {
    return this.service.getAll()
  }

  public async createRecordInBlazeCanada(
    api: BlazeCanadaAPI,
    record: TAOCategoryContract,
  ): Promise<string> {
    const blazeCanadaContract: TCreateBlazeCanadaCategory = {
      name: record.name,
      platformComplianceCategoryId: record.complianceCategoryId,
      isActive: record.isActive,
      cannabisType: record.cannabisType,
      imageUrl: record.imageUrl,
      measurementType: this.getBlazeCanadaMeasurementType(record.unitType),
      categoryType: CategoryType.STANDARD,
      lowInventoryThreshold: record.lowInventoryThreshold,
    }

    const response = await api.createCategory(this.companyId, blazeCanadaContract)
    return response.id
  }
}
