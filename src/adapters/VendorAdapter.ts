import { BlazeCanadaAPI } from '../apis/BlazeCanadaAPI'
import { DomainAdapter } from '../models/adapters/DomainAdapter'
import { TCreateBlazeCanadaVendor } from '../models/apis/blaze-canada/Vendor'
import { TAOVendorContract } from '../models/contracts/auto-onboarding/Vendor/VendorContract'
import {
  VendorExportRowFields,
  TVendorExportRow,
  TDedupedVendorExportRow,
  parseVendorExport,
} from '../models/contracts/auto-onboarding/Vendor/VendorExportRow'
import { AOVendorService } from '../services/auto-onboarding/AOVendorService'
import { groupedRecordsByKey, hashCanonicalStr, unifyRecordGroup } from './utils'

export class VendorAdapter
  implements DomainAdapter<TVendorExportRow, TAOVendorContract, never, false>
{
  private companyId: string
  private service: AOVendorService

  constructor(companyId: string) {
    this.companyId = companyId
    this.service = new AOVendorService(companyId)
  }

  private getIdFromExportRow(record: TVendorExportRow): string {
    return record.id
  }

  public isBulkDomain(): false {
    return false
  }

  public parse = parseVendorExport

  public getIdFromContract(contract: TAOVendorContract): string {
    return contract.id
  }

  public getDedupeKeyFromContract(contract: TAOVendorContract): string {
    return contract.key
  }

  public csvKey() {
    return `companies/${this.companyId}/vendors/company-${this.companyId}-vendors-export.csv`
  }

  public dedupeKey(record: TVendorExportRow): string {
    return hashCanonicalStr(record.name)
  }

  public unifyMatchingRecords(records: TVendorExportRow[]): TDedupedVendorExportRow[] {
    const recordGroupsMap = groupedRecordsByKey<TVendorExportRow>(records, this.dedupeKey)
    const unifiedRecords: TDedupedVendorExportRow[] = []
    const recordGroups = recordGroupsMap.entries()

    for (const [key, group] of recordGroups) {
      const unifiedRecord = unifyRecordGroup(key, group, {
        getId: this.getIdFromExportRow,
        fields: VendorExportRowFields,
      })
      unifiedRecords.push(unifiedRecord)
    }

    return unifiedRecords
  }

  public async saveRecords(records: TDedupedVendorExportRow[]): Promise<void> {
    await this.service.bulkCreate(records)
  }

  public async getSavedRecords(): Promise<TAOVendorContract[]> {
    return this.service.getAll()
  }

  public async createRecordInBlazeCanada(
    api: BlazeCanadaAPI,
    record: TAOVendorContract,
  ): Promise<string> {
    const blazeCanadaContract: TCreateBlazeCanadaVendor = {
      companyId: parseInt(this.companyId),
      name: record.name,
      address: {
        address: record.addressStreet,
        city: record.addressCity,
        state: record.addressState,
        zip: record.addressZip,
      },
      phone: record.phone,
      fax: record.fax,
      email: record.email,
      website: record.website,
      description: record.description,
      license: record.license,
      licenseExpiration: record.licenseExpiration,
      contactFirstName: record.contractFirstName,
      contactLastName: record.contractLastName,
    }

    const response = await api.createVendor(this.companyId, blazeCanadaContract)
    return response.id
  }
}
