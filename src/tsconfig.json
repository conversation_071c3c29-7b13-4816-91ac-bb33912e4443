{
  "compilerOptions": {
    "strictNullChecks": true,
    "noImplicitThis": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noImplicitReturns": false,
    "noFallthroughCasesInSwitch": false,
    "inlineSourceMap": true,
    "inlineSources": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "strictPropertyInitialization": false,
    "downlevelIteration": true,
    "esModuleInterop": true,
    "target": "es2022",
    "moduleResolution": "Bundler",
    "types": ["jest", "node"],
    "skipLibCheck": true,
  },
  "include": ["**/*"],
}
