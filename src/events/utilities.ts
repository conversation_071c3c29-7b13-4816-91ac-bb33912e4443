import { IEventPayload } from '../models/events/interfaces'
import {
  PutEventsCommand,
  PutEventsRequestEntry,
  PutEventsResponse,
} from '@aws-sdk/client-eventbridge'
import { eventBridgeClient } from '../common/aws-sdk-clients/clients'
import { logger } from '../common/powertools/logger'
import { chunk } from 'lodash'
import { ResourceType } from '../serializers/enums'
import { EventDetailType } from '../models/events/enums'
import { CommonConverter } from '../converters/CommonConverter'

export abstract class AwsEventPublisher {
  static async publish<T>(
    resources: T | T[],
    resourceType: ResourceType,
    eventDetailType: EventDetailType,
  ): Promise<void> {
    const resourceArray = Array.isArray(resources) ? resources : [resources]
    const events = resourceArray.map((resource) =>
      CommonConverter.toEvent(resource, resourceType, eventDetailType),
    )

    const awsEvents = AwsEventPublisher.mapEvents(events)
    await AwsEventPublisher.sendToEventBridge(awsEvents)
  }

  private static mapEvents = <T>(events: IEventPayload<T>[]): PutEventsRequestEntry[] => {
    return events.map((e) => {
      const payload = {
        ...e.jsonDumps,
      }

      const eventRequest: PutEventsRequestEntry = {
        EventBusName: process.env.EVENT_BUS_NAME,
        Source: 'me.blaze.platform.product-service',
        DetailType: e.eventDetailType,
        Detail: JSON.stringify(payload),
      }

      return eventRequest
    })
  }

  static sendToEventBridge = async (
    awsEvents: PutEventsRequestEntry[],
  ): Promise<PutEventsResponse[] | void> => {
    try {
      const chunkedEvents = chunk(awsEvents, 10)

      const promises = chunkedEvents.map((chunkedEvent) => {
        const putEventsCommand = new PutEventsCommand({
          Entries: chunkedEvent,
        })

        return eventBridgeClient.send(putEventsCommand)
      })

      await Promise.all(promises)

      logger.info('event', { awsEvents })
    } catch (error) {
      logger.error('event', { error: JSON.stringify(error) })
    }
  }
}
