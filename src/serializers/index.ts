import JSONAPISerializer from 'json-api-serializer'
import { ResourceType } from './enums'

const Serializer = new JSONAPISerializer()

Serializer.register(ResourceType.PRODUCT, {
  id: 'id',
  jsonapiObject: false,
  topLevelMeta: (extraData) => ({
    searchAfter: extraData.searchAfter,
    count: extraData.count,
  }),
})

Serializer.register(ResourceType.CATEGORY, {
  id: 'id',
  jsonapiObject: false,
})

Serializer.register(ResourceType.PRODUCT_EVENT, {
  id: 'id',
  jsonapiObject: false,
})

Serializer.register(ResourceType.CATEGORY_EVENT, {
  id: 'id',
  jsonapiObject: false,
})

Serializer.register(ResourceType.PRODUCT_PRICE, {
  id: 'globalProductId',
  jsonapiObject: false,
})

Serializer.register(ResourceType.PRODUCT_PRICE_EVENT, {
  id: 'globalProductId',
  jsonapiObject: false,
})

Serializer.register(ResourceType.INVENTORY_INFO, {
  id: 'id',
  jsonapiObject: false,
})

export default Serializer
