import middy from '@middy/core'
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda/trigger/api-gateway-proxy'
import { persistLogAttributes, logger, removeAllLogAttributes } from '../common/powertools/logger'
import { PersistentLogKeys } from '../common/powertools/enums'

export const LoggerMiddleware: middy.MiddlewareObj<APIGatewayProxyEvent, APIGatewayProxyResult> = {
  before: async (handler): Promise<void> => {
  },
  after: (handler) => {
    removeAllLogAttributes()
  },
  onError: (handler) => {
    const e = handler?.error as any

    persistLogAttributes([{ 
      key: PersistentLogKeys.HTTP_STATUS, 
      value: e?.statusCode || 500 
    }])

    if (e) logger.error('client.error.response', { error: e })
    removeAllLogAttributes()
  },
}
