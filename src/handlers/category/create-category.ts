import { APIGatewayProxyEvent } from 'aws-lambda'
import { middyWrapper } from '../../common/middy/middy'
import { ResponseWriter } from '../../common/web/ResponseWriter'
import { CommonConverter } from '../../converters/CommonConverter'
import { MerchantIdPathParameter } from '../../dto/common'
import Serializer from '../../serializers'
import { ResourceType } from '../../serializers/enums'
import { CreateCategoryReq } from '../../dto/category'
import { CategoryAction } from '../../actions/CategoryAction'

/**
 * Endpoint: POST /merchants/{merchantId}/categories
 */
export const createCategory = middyWrapper<APIGatewayProxyEvent>(async (event) => {
  const pathReq = CommonConverter.toDto(event.pathParameters, MerchantIdPathParameter)
  const bodyReq = CommonConverter.eventBodyToDto(
    event.body,
    CreateCategoryReq,
    ResourceType.CATEGORY,
  )
  const res = await CategoryAction.createCategory(pathReq, bodyReq)
  const jsonApiResponse = Serializer.serialize(ResourceType.CATEGORY, res)
  return ResponseWriter.objectResponse(200, jsonApiResponse)
})
