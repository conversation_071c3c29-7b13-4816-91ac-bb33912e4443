import { APIGatewayProxyEvent } from 'aws-lambda'
import { middyWrapper } from '../../common/middy/middy'
import { ResponseWriter } from '../../common/web/ResponseWriter'
import { CommonConverter } from '../../converters/CommonConverter'
import Serializer from '../../serializers'
import { ResourceType } from '../../serializers/enums'
import { CategoryIdPathReq, UpdateCategoryReq } from '../../dto/category'
import { CategoryAction } from '../../actions/CategoryAction'

/**
 * Endpoint: PUT /merchants/{merchantId}/categories/{categoryId}
 */
export const updateCategory = middyWrapper<APIGatewayProxyEvent>(async (event) => {
  const pathReq = CommonConverter.toDto(event.pathParameters, CategoryIdPathReq)
  const bodyReq = CommonConverter.eventBodyToDto(
    event.body,
    UpdateCategoryReq,
    ResourceType.CATEGORY,
  )
  const res = await CategoryAction.updateCategory(pathReq, bodyReq)
  const jsonApiResponse = Serializer.serialize(ResourceType.CATEGORY, res)
  return ResponseWriter.objectResponse(200, jsonApiResponse)
})
