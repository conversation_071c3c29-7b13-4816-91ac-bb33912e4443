import { APIGatewayProxyEvent } from 'aws-lambda'
import { middyWrapper } from '../../common/middy/middy'
import { ResponseWriter } from '../../common/web/ResponseWriter'
import { CommonConverter } from '../../converters/CommonConverter'
import { ResourceType } from '../../serializers/enums'
import Serializer from '../../serializers'
import { CategoryAction } from '../../actions/CategoryAction'
import { MerchantIdPathParameter } from '../../dto/common'

/**
 * Endpoint: GET /merchants/{merchantId}/categories
 */
export const listCategories = middyWrapper<APIGatewayProxyEvent>(async (event) => {
  const pathReq = CommonConverter.toDto(event.pathParameters, MerchantIdPathParameter)
  const res = await CategoryAction.listCategories(pathReq)
  const jsonApiResponse = Serializer.serialize(ResourceType.CATEGORY, res)
  return ResponseWriter.objectResponse(200, jsonApiResponse)
})
