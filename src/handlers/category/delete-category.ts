import { APIGatewayProxyEvent } from 'aws-lambda'
import { middyWrapper } from '../../common/middy/middy'
import { ResponseWriter } from '../../common/web/ResponseWriter'
import { CommonConverter } from '../../converters/CommonConverter'
import { CategoryAction } from '../../actions/CategoryAction'
import { CategoryIdPathReq } from '../../dto/category'
import { HttpStatusCode } from 'axios'

/**
 * Endpoint: DELETE /merchants/{merchantId}/categories/{categoryId}
 */
export const deleteCategory = middyWrapper<APIGatewayProxyEvent>(async (event) => {
  const pathReq = CommonConverter.toDto(event.pathParameters, CategoryIdPathReq)
  await CategoryAction.deleteCategory(pathReq)

  return ResponseWriter.objectResponse(HttpStatusCode.NoContent)
})
