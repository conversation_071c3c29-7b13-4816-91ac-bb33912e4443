import { APIGatewayProxyEvent } from 'aws-lambda'
import { middyWrapper } from '../../common/middy/middy'
import { ResponseWriter } from '../../common/web/ResponseWriter'
import { CommonConverter } from '../../converters/CommonConverter'
import { ResourceType } from '../../serializers/enums'
import Serializer from '../../serializers'
import { CategoryIdPathReq } from '../../dto/category'
import { CategoryService } from '../../services/CategoryService'

/**
 * Endpoint: GET /merchants/{merchantId}/categories/{categoryId}
 */
export const getCategory = middyWrapper<APIGatewayProxyEvent>(async (event) => {
  const pathReq = CommonConverter.toDto(event.pathParameters, CategoryIdPathReq)
  const res = await CategoryService.findCategory(pathReq)
  const jsonApiResponse = Serializer.serialize(ResourceType.CATEGORY, res)
  return ResponseWriter.objectResponse(200, jsonApiResponse)
})