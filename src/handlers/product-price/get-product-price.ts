import { APIGatewayProxyEvent } from 'aws-lambda'
import { middyWrapper } from '../../common/middy/middy'
import { ResponseWriter } from '../../common/web/ResponseWriter'
import { CommonConverter } from '../../converters/CommonConverter'
import Serializer from '../../serializers'
import { ResourceType } from '../../serializers/enums'
import { ProductPriceIdPathReq, UpdateProductPriceReq } from '../../dto/product-price'
import { ProductPriceService } from '../../services/ProductPriceService'

/**
 * Endpoint: GET /merchants/{merchantId}/products/{productId}/prices
 */
export const getProductPrice = middyWrapper<APIGatewayProxyEvent>(async (event) => {
  const pathReq = CommonConverter.toDto(event.pathParameters, ProductPriceIdPathReq)
  const res = await ProductPriceService.getProductPrice(pathReq)
  const jsonApiResponse = Serializer.serialize(ResourceType.PRODUCT_PRICE, res)
  return ResponseWriter.objectResponse(200, jsonApiResponse)
})
