import { APIGatewayProxyEvent } from 'aws-lambda'
import { middyWrapper } from '../../common/middy/middy'
import { ResponseWriter } from '../../common/web/ResponseWriter'
import { CommonConverter } from '../../converters/CommonConverter'
import Serializer from '../../serializers'
import { ResourceType } from '../../serializers/enums'
import { ProductPriceIdPathReq, UpdateProductPriceReq } from '../../dto/product-price'
import { invalidParameterError } from '../../common/error'
import { ProductPriceAction } from '../../actions/ProductPriceAction'

/**
 * Endpoint: POST /merchants/{merchantId}/products/{productId}/prices
 */
export const createProductPrice = middyWrapper<APIGatewayProxyEvent>(async (event) => {
  const pathReq = CommonConverter.toDto(event.pathParameters, ProductPriceIdPathReq)
  const bodyReq = CommonConverter.eventBodyToDto(event.body, UpdateProductPriceReq, ResourceType.PRODUCT_PRICE)
  if (bodyReq.companyProductId !== pathReq.productId) {
    throw invalidParameterError('Path parameter and body parameter must match')
  }
  const res = await ProductPriceAction.createProductPrice(pathReq, bodyReq)
  const jsonApiResponse = Serializer.serialize(ResourceType.PRODUCT_PRICE, res)
  return ResponseWriter.objectResponse(200, jsonApiResponse)
})