import { APIGatewayProxyEvent } from 'aws-lambda'
import { middyWrapper } from '../../common/middy/middy'
import { ResponseWriter } from '../../common/web/ResponseWriter'
import { ProductAction } from '../../actions/ProductAction'
import { CommonConverter } from '../../converters/CommonConverter'
import { UpdateProductReq } from '../../dto/product'
import { MerchantIdPathParameter } from '../../dto/common'
import Serializer from '../../serializers'
import { ResourceType } from '../../serializers/enums'

/**
 * Endpoint: POST /merchants/{merchantId}/products
 */
export const createProduct = middyWrapper<APIGatewayProxyEvent>(async (event) => {
  const pathReq = CommonConverter.toDto(event.pathParameters, MerchantIdPathParameter)
  const bodyReq = CommonConverter.eventBodyToDto(event.body, UpdateProductReq, ResourceType.PRODUCT)
  const res = await ProductAction.createProduct(pathReq, bodyReq)
  const jsonApiResponse = Serializer.serialize(ResourceType.PRODUCT, res)
  return ResponseWriter.objectResponse(200, jsonApiResponse)
})
