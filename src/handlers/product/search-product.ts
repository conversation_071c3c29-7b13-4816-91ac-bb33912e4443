import { APIGatewayProxyEvent } from 'aws-lambda'
import { middyWrapper } from '../../common/middy/middy'
import { ResponseWriter } from '../../common/web/ResponseWriter'
import { CommonConverter } from '../../converters/CommonConverter'
import { ResourceType } from '../../serializers/enums'
import Serializer from '../../serializers'
import { SearchProductReq } from '../../dto/product-search'
import { SearchProductService } from '../../services/SearchProductService'
import { MerchantIdPathParameter } from '../../dto/common'

/**
 * Endpoint: POST /merchants/{merchantId}/products/search
 */
export const searchProduct = middyWrapper<APIGatewayProxyEvent>(async (event) => {
  const pathReq = CommonConverter.toDto(event.pathParameters, MerchantIdPathParameter)
  const bodyReq = CommonConverter.eventBodyToDto(event.body, SearchProductReq, ResourceType.PRODUCT)
  const { products, ...meta } = await SearchProductService.searchProducts(pathReq, bodyReq)
  const jsonApiResponse = Serializer.serialize(ResourceType.PRODUCT, products, meta)
  return ResponseWriter.objectResponse(200, jsonApiResponse)
})
