import { APIGatewayProxyEvent } from 'aws-lambda'
import { middyWrapper } from '../../common/middy/middy'
import { ResponseWriter } from '../../common/web/ResponseWriter'
import { CommonConverter } from '../../converters/CommonConverter'
import { ResourceType } from '../../serializers/enums'
import Serializer from '../../serializers'
import { ProductIdPathReq } from '../../dto/product'
import { ProductService } from '../../services/ProductService'

/**
 * Endpoint: GET /merchants/{merchantId}/products/{productId}
 */
export const getProduct = middyWrapper<APIGatewayProxyEvent>(async (event) => {
  const pathReq = CommonConverter.toDto(event.pathParameters, ProductIdPathReq)
  const res = await ProductService.findProduct(pathReq)
  const jsonApiResponse = Serializer.serialize(ResourceType.PRODUCT, res)
  return ResponseWriter.objectResponse(200, jsonApiResponse)
})
