import { APIGatewayProxyEvent } from 'aws-lambda'
import { middyWrapper } from '../../common/middy/middy'
import { ResponseWriter } from '../../common/web/ResponseWriter'
import { ProductAction } from '../../actions/ProductAction'
import { CommonConverter } from '../../converters/CommonConverter'
import { ProductIdPathReq } from '../../dto/product'
import { HttpStatusCode } from 'axios'

/**
 * Endpoint: DELETE /merchants/{merchantId}/products/{productId}
 */
export const deleteProduct = middyWrapper<APIGatewayProxyEvent>(async (event) => {
  const pathReq = CommonConverter.toDto(event.pathParameters, ProductIdPathReq)
  await ProductAction.deleteProduct(pathReq)
  return ResponseWriter.objectResponse(HttpStatusCode.NoContent)
})
