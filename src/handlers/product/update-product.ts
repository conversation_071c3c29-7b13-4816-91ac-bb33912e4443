import { APIGatewayProxyEvent } from 'aws-lambda'
import { middyWrapper } from '../../common/middy/middy'
import { ResponseWriter } from '../../common/web/ResponseWriter'
import { ProductAction } from '../../actions/ProductAction'
import { CommonConverter } from '../../converters/CommonConverter'
import { ProductIdPathReq, UpdateProductReq } from '../../dto/product'
import Serializer from '../../serializers'
import { ResourceType } from '../../serializers/enums'

/**
 * Endpoint: PUT /merchants/{merchantId}/products/{productId}
 */
export const updateProduct = middyWrapper<APIGatewayProxyEvent>(async (event) => {
  const pathReq = CommonConverter.toDto(event.pathParameters, ProductIdPathReq)
  const bodyReq = CommonConverter.eventBodyToDto(event.body, UpdateProductReq, ResourceType.PRODUCT)
  const res = await ProductAction.updateProduct(pathReq.productId, bodyReq)
  const jsonApiResponse = Serializer.serialize(ResourceType.PRODUCT, res)
  return ResponseWriter.objectResponse(200, jsonApiResponse)
})
