import { APIGatewayProxyEvent } from 'aws-lambda'
import { middyWrapper } from '../../common/middy/middy'
import { ResponseWriter } from '../../common/web/ResponseWriter'
import { parseStartSyncEnv } from '../../models/envs/start-sync'
import { parseStartSyncEvent } from '../../models/events/lambdas/start-sync'
import { startStateMachineExecution } from '../../common/aws-sdk-clients/sfn/sfn'
import { Logger } from '@aws-lambda-powertools/logger'
import { Metrics } from '@aws-lambda-powertools/metrics'
import { Tracer } from '@aws-lambda-powertools/tracer'
import { injectLambdaContext } from '@aws-lambda-powertools/logger/middleware'
import { logMetrics } from '@aws-lambda-powertools/metrics/middleware'
import { captureLambdaHandler } from '@aws-lambda-powertools/tracer/middleware'
import { BlazeCanadaAPI } from '../../apis/BlazeCanadaAPI'
import { getSecretValue } from '../../common/aws-sdk-clients/sm/sm'
import { parseBlazeCanadaAPIKeySecretSchema } from '../../models/secrets/BlazeCanadaAPIKey'
import { EPOSProvider } from '../../models/common/enums'

/**
 * Endpoint: POST /companies/{companyId}/sync
 */

// ──────────────────────────────────────────

const logger = new Logger({ logLevel: 'DEBUG' })
const tracer = new Tracer()
const metrics = new Metrics({ namespace: 'AutoOnboarding' })

// ──────────────────────────────────────────

export const startSync = middyWrapper<APIGatewayProxyEvent>(async (event: unknown) => {
  try {
    const env = parseStartSyncEnv(process.env)
    const parsedEvent = parseStartSyncEvent(event)
    const {
      pathParameters: { companyId },
      body: { domains, force },
    } = parsedEvent

    if (!force) {
      const apiKeySecretValue = await getSecretValue(env.BLAZE_CANADA_API_KEY_SECRET_NAME)
      const parsedApiKeySecretValue = parseBlazeCanadaAPIKeySecretSchema(apiKeySecretValue)
      const blazeCanadaAPI = new BlazeCanadaAPI(parsedApiKeySecretValue.apiKey, EPOSProvider.US)

      const existingCategories = await blazeCanadaAPI.getCategories(companyId)
      if (existingCategories.length > 0) {
        logger.warn('Categories already exist, skipping state machine execution')
        return ResponseWriter.objectResponse(400, {
          message: `This company already has categories. Please make sure the target company is ${companyId} and retry with 'force: true' query parameter.`,
        })
      }
    }

    const { executionArn } = await startStateMachineExecution(env.STATE_MACHINE_ARN, {
      companyId,
      executionDomains: domains,
    })
    logger.info('Started state machine execution', { executionArn })

    return ResponseWriter.objectResponse(200, { executionArn })
  } catch (error) {
    logger.error('Error during execution', { error })
    return ResponseWriter.objectResponse(400, {
      message: 'There was an error during execution',
      error,
    })
  }
})
  .use(injectLambdaContext(logger))
  .use(captureLambdaHandler(tracer))
  .use(logMetrics(metrics))
