import middy from '@middy/core'
import { Logger } from '@aws-lambda-powertools/logger'
import { Tracer } from '@aws-lambda-powertools/tracer'
import { Metrics, MetricUnit } from '@aws-lambda-powertools/metrics'
import { injectLambdaContext } from '@aws-lambda-powertools/logger/middleware'
import { logMetrics } from '@aws-lambda-powertools/metrics/middleware'
import { captureLambdaHandler } from '@aws-lambda-powertools/tracer/middleware'
import type { APIGatewayProxyEventV2 } from 'aws-lambda'

import { getS3ContentString } from '../../common/aws-sdk-clients/s3/s3'
import { parseCsv } from '../../utils/csv'

import { parseIngestFileEvent } from '../../models/events/lambdas/ingest-file'
import { parseIngestFileEnv } from '../../models/envs/ingest-file'
import { getAdapter } from '../../switchers/DomainSwitcher'

// ──────────────────────────────────────────

const logger = new Logger({ logLevel: 'DEBUG' })
const tracer = new Tracer()
const metrics = new Metrics({ namespace: 'AutoOnboarding' })

// ──────────────────────────────────────────

const handler = async (event: APIGatewayProxyEventV2 | any) => {
  logger.debug('Raw event', { event })

  const env = parseIngestFileEnv(process.env)
  logger.info('Parsed environment variables')
  const parsedEvent = parseIngestFileEvent(event)
  logger.info('Parsed event', { event: parsedEvent })

  const { companyId, domain, executionDomains } = parsedEvent
  logger.appendKeys({ companyId, domain })

  const adapter = getAdapter(companyId, domain)

  if (!executionDomains.includes(domain)) {
    logger.info('Current domain is not in execution domains, skipping...', {
      domain,
      executionDomains,
    })
    return { companyId, domain, batches: adapter.isBulkDomain() ? [] : undefined }
  }

  const csvStr = await getS3ContentString(env.SOURCE_FILES_BUCKET_NAME, adapter.csvKey())
  logger.debug('Fetched CSV file content', {
    bucketName: env.SOURCE_FILES_BUCKET_NAME,
    csvKey: adapter.csvKey(),
  })

  const rawRows = await parseCsv(csvStr, { headers: true, trim: true })
  logger.debug('Parsed CSV string', { rowsSample: rawRows.slice(0, 3) })

  const parsedRows = adapter.parse(rawRows)
  logger.info('Validated rows')

  const uniqueRecords = adapter.unifyMatchingRecords(parsedRows)
  logger.info('Converted to unique records', {
    recordsSample: uniqueRecords.slice(0, 3),
  })

  const batches = await adapter.saveRecords(uniqueRecords)
  logger.info('Records saved')

  metrics.addMetric('RowsTotal', MetricUnit.Count, parsedRows.length)
  metrics.addMetric('UniqueRecordsTotal', MetricUnit.Count, uniqueRecords.length)

  return { companyId, domain, batches }
}

export const ingestFile = middy(handler)
  .use(injectLambdaContext(logger))
  .use(captureLambdaHandler(tracer))
  .use(logMetrics(metrics))
