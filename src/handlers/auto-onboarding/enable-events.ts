import { Logger } from '@aws-lambda-powertools/logger'
import { Metrics, MetricUnit } from '@aws-lambda-powertools/metrics'
import {
  SSMClient,
  PutParameterCommand,
  GetParameterCommand,
  GetParameterCommandOutput,
} from '@aws-sdk/client-ssm'
import { PutRuleCommand } from '@aws-sdk/client-eventbridge'
import { eventBridgeClient } from '../../common/aws-sdk-clients/clients'
import { parseEnableEventsEnv } from '../../models/envs/enable-events'
import { parseEnableEventsEvent } from '../../models/events/lambdas/enable-events'

const logger = new Logger({ logLevel: 'DEBUG' })
const metrics = new Metrics({ namespace: 'AutoOnboarding' })

export const enableEvents = async (event: unknown) => {
  const env = parseEnableEventsEnv(process.env)
  const parsedEvent = parseEnableEventsEvent(event)

  const { companyId } = parsedEvent

  const paramName = '/platform-product-events/companyIds'
  const client = new SSMClient({ region: 'us-east-1' })

  const getParameterCommand = new GetParameterCommand({
    Name: paramName,
    WithDecryption: true,
  })

  let existingCompanyIds: string = ''
  try {
    const response: GetParameterCommandOutput = await client.send(getParameterCommand)
    if (response.Parameter?.Value) {
      existingCompanyIds = response.Parameter.Value
    }
  } catch (error) {
    if (error.name !== 'ParameterNotFound') {
      logger.error('Error getting parameter', { error })
      throw error
    }
  }

  logger.info('Existing company IDs', { existingCompanyIds })
  const newCompanyIds = `${existingCompanyIds}, ${companyId}`
  logger.info('New company IDs', { newCompanyIds })

  const putParameterCommand = new PutParameterCommand({
    Name: paramName,
    Value: newCompanyIds,
    Type: 'String',
    Overwrite: true,
  })

  try {
    await client.send(putParameterCommand)
    logger.info('Successfully updated parameter', { paramName, newCompanyIds })
  } catch (error) {
    logger.error('Error updating parameter', { error })
    throw error
  }
  metrics.addMetric('UpdatedCompanyIds', MetricUnit.Count, 1)
  metrics.addMetric('TotalCompanyIds', MetricUnit.Count, newCompanyIds.length)

  const putRuleCommand = new PutRuleCommand({
    EventPattern: JSON.stringify({
      'detail-type': [
        'category.created',
        'category.updated',
        'category.deleted',
        'product.created',
        'product.updated',
        'product.deleted',
        'discount.created',
        'discount.updated',
        'discount.deleted',
        'vendor.created',
        'vendor.updated',
        'vendor.deleted',
        'brand.created',
        'brand.updated',
        'brand.deleted',
      ],
      detail: {
        companyId: newCompanyIds.split(',').map((id) => Number(id.trim())),
      },
      source: ['co.getgreenline.api'],
    }),
    State: 'ENABLED',
    Description: 'Process product related events from GL platform',
    Name: env.PRODUCT_EVENT_RULE_NAME,
    EventBusName: env.PLATFORM_PRODUCT_EVENT_BUS_NAME,
  })

  try {
    const response = await eventBridgeClient.send(putRuleCommand)
    logger.info('Successfully updated event rule', {
      eventRuleName: env.PRODUCT_EVENT_RULE_NAME,
      response,
    })
  } catch (error) {
    logger.error('Error creating event rule', { error })
    throw error
  }
  metrics.addMetric('UpdatedEventRule', MetricUnit.Count, 1)

  return { companyId }
}
