import { Logger } from '@aws-lambda-powertools/logger'
import { Metrics, MetricUnit } from '@aws-lambda-powertools/metrics'
import {
  SSMClient,
  PutParameterCommand,
  GetParameterCommand,
  GetParameterCommandOutput,
} from '@aws-sdk/client-ssm'
import { PutRuleCommand } from '@aws-sdk/client-eventbridge'
import { eventBridgeClient } from '../../common/aws-sdk-clients/clients'
import { parseEnableEventsEnv } from '../../models/envs/enable-events'
import { parseEnableEventsEvent } from '../../models/events/lambdas/enable-events'

const logger = new Logger({ logLevel: 'DEBUG' })
const metrics = new Metrics({ namespace: 'AutoOnboarding' })

export const enableEvents = async (event: unknown) => {
  const env = parseEnableEventsEnv(process.env)
  const parsedEvent = parseEnableEventsEvent(event)

  const { companyId } = parsedEvent

  const paramName = '/platform-product-events/companyIds'
  const client = new SSMClient({ region: 'us-east-1' })

  const getParameterCommand = new GetParameterCommand({
    Name: paramName,
    WithDecryption: true,
  })

  let existingCompanyIds: string = ''
  try {
    const response: GetParameterCommandOutput = await client.send(getParameterCommand)
    if (response.Parameter?.Value) {
      existingCompanyIds = response.Parameter.Value
    }
  } catch (error) {
    if (error.name !== 'ParameterNotFound') {
      logger.error('Error getting parameter', { error })
      throw error
    }
  }

  //Remove duplicates from the existingCompanyIds
  logger.info('Existing company IDs', { existingCompanyIds })
  const newCompanyIds = `${existingCompanyIds}, ${companyId}`
  const newCompanyIdsArray = newCompanyIds.split(',')
  const uniqueNewCompanyIds = [...new Set(newCompanyIdsArray)]
  const uniqueNewCompanyIdsString = uniqueNewCompanyIds.join(',')
  logger.info('New company IDs', { uniqueNewCompanyIdsString })

  const putParameterCommand = new PutParameterCommand({
    Name: paramName,
    Value: uniqueNewCompanyIdsString,
    Type: 'String',
    Overwrite: true,
  })

  try {
    await client.send(putParameterCommand)
    logger.info('Successfully updated parameter', { paramName, uniqueNewCompanyIdsString })
  } catch (error) {
    logger.error('Error updating parameter', { error })
    throw error
  }
  metrics.addMetric('UpdatedCompanyIds', MetricUnit.Count, 1)
  metrics.addMetric('TotalCompanyIds', MetricUnit.Count, uniqueNewCompanyIds.length)

  const glPutRuleCommand = new PutRuleCommand({
    EventPattern: JSON.stringify({
      'detail-type': [
        'category.created',
        'category.updated',
        'category.deleted',
        'product.created',
        'product.updated',
        'product.deleted',
        'discount.created',
        'discount.updated',
        'discount.deleted',
        'vendor.created',
        'vendor.updated',
        'vendor.deleted',
        'brand.created',
        'brand.updated',
        'brand.deleted',
      ],
      detail: {
        companyId: uniqueNewCompanyIds.map((id) => Number(id.trim())),
      },
      source: ['co.getgreenline.api'],
    }),
    State: 'ENABLED',
    Description: 'Process product related events from GL platform',
    Name: env.GL_PRODUCT_EVENT_RULE_NAME,
    EventBusName: env.PLATFORM_PRODUCT_EVENT_BUS_NAME,
  })

  const usPutRuleCommand = new PutRuleCommand({
    EventPattern: JSON.stringify({
      'detail-type': [
        'shop_product.created',
        'shop_product.updated',
      ],
      detail: {
        data: {
          attributes: {
            company: {
              platformCompanyId: uniqueNewCompanyIds.map((id) => id.trim()),
            },
          },
        },
      },
      source: ['me.blaze.api'],
    }),
    State: 'ENABLED',
    Description: 'Process shop product events from Blaze US',
    Name: env.US_PRODUCT_EVENT_RULE_NAME,
    EventBusName: env.PLATFORM_PRODUCT_EVENT_BUS_NAME,
  })

  try {
    const glResponse = await eventBridgeClient.send(glPutRuleCommand)
    logger.info('Successfully updated event rule', {
      eventRuleName: env.GL_PRODUCT_EVENT_RULE_NAME,
      glResponse,
    })
  } catch (error) {
    logger.error('Error creating GL event rule', { error })
    throw error
  }

  try {
    const usResponse = await eventBridgeClient.send(usPutRuleCommand)
    logger.info('Successfully updated event rule', {
      eventRuleName: env.US_PRODUCT_EVENT_RULE_NAME,
      usResponse,
    })
  } catch (error) {
    logger.error('Error creating US event rule', { error })
    throw error
  }
  metrics.addMetric('UpdatedEventRule', MetricUnit.Count, 1)

  return { companyId }
}
