import middy from '@middy/core'
import { Logger } from '@aws-lambda-powertools/logger'
import { Tracer } from '@aws-lambda-powertools/tracer'
import { Metrics, MetricUnit } from '@aws-lambda-powertools/metrics'
import { injectLambdaContext } from '@aws-lambda-powertools/logger/middleware'
import { captureLambdaHandler } from '@aws-lambda-powertools/tracer/middleware'
import { logMetrics } from '@aws-lambda-powertools/metrics/middleware'

import { parseImportBatchEnv } from '../../models/envs/import-batch'
import { getSecretValue } from '../../common/aws-sdk-clients/sm/sm'
import { parseBlazeCanadaAPIKeySecretSchema } from '../../models/secrets/BlazeCanadaAPIKey'
import { getAdapter } from '../../switchers/DomainSwitcher'
import { parseImportBatchEvent } from '../../models/events/lambdas/import-batch'
import { BlazeCanadaAPI } from '../../apis/BlazeCanadaAPI'
import { EPOSProvider } from '../../models/common/enums'

const logger = new Logger({ logLevel: 'DEBUG' })
const tracer = new Tracer()
const metrics = new Metrics({ namespace: 'AutoOnboarding' })

const handler = async (event: unknown) => {
  logger.debug('Ingested event', { event })
  const env = parseImportBatchEnv(process.env)
  const parsedEvent = parseImportBatchEvent(event)
  const { companyId, domain, executionDomains, batch } = parsedEvent

  logger.appendKeys({ companyId, domain, batch })

  logger.info('Parsed event', { event: parsedEvent })

  if (!executionDomains.includes(domain)) {
    logger.info('Current domain is not in execution domains, skipping...', {
      domain,
      executionDomains,
    })
    return
  }

  const adapter = getAdapter(companyId, domain)

  const apiKeySecretValue = await getSecretValue(env.BLAZE_CANADA_API_KEY_SECRET_NAME)
  const parsedApiKeySecretValue = parseBlazeCanadaAPIKeySecretSchema(apiKeySecretValue)
  const blazeCanadaAPI = new BlazeCanadaAPI(parsedApiKeySecretValue.apiKey, EPOSProvider.US)

  const imported = new Set<string>()

  if (adapter.isBulkDomain() && adapter.bulkCreateRecordsInBlazeCanada && batch) {
    const records = await adapter.getSavedRecordsByBatch(batch)
    logger.info('Fetched records by batch', { recordsSample: records.slice(0, 3) })

    await adapter.bulkCreateRecordsInBlazeCanada(records, {
      api: blazeCanadaAPI,
      bucketName: env.BLAZE_CANADA_BULK_CSV_BUCKET_NAME,
      stage: env.STAGE,
      batchNumber: batch,
    })
    logger.info('Bulk created records in Blaze Canada')
  }

  if (!adapter.isBulkDomain() && adapter.createRecordInBlazeCanada) {
    const records = await adapter.getSavedRecords()
    logger.info('Fetched records', { recordsSample: records.slice(0, 3) })

    for (const uniqueRecord of records) {
      await adapter.createRecordInBlazeCanada(blazeCanadaAPI, uniqueRecord)
      imported.add(adapter.getIdFromContract(uniqueRecord))
    }

    logger.info('Created records in Blaze Canada', { imported: imported.size })

    if (imported.size < records.length) {
      logger.warn('Not all records were imported!', {
        imported: imported.size,
        total: records.length,
        missing: records.filter((record) => !imported.has(adapter.getIdFromContract(record))),
      })
    }

    metrics.addMetric('FailedImports', MetricUnit.Count, imported.size - records.length)
  }
  metrics.addMetric('ImportedRecords', MetricUnit.Count, imported.size)

  return { companyId, domain }
}

export const importBatch = middy(handler)
  .use(injectLambdaContext(logger))
  .use(captureLambdaHandler(tracer))
  .use(logMetrics(metrics))
