import { MetricUnit } from '@aws-lambda-powertools/metrics'
import { APIGatewayProxyEvent } from 'aws-lambda'
import { logger } from '../../common/powertools/logger'
import { EMetricNames, metrics } from '../../common/powertools/metrics'
import { ResponseWriter } from '../../common/web/ResponseWriter'

export const handler = async (event: APIGatewayProxyEvent) => {
  try {
    logger.info('Skipping query, logic to be added.')
  } catch (error) {
    metrics.addMetric(EMetricNames.QUERY_FATAL_ERROR, MetricUnit.Count, 1)
    metrics.publishStoredMetrics()
    logger.error(JSON.stringify(error))
    return ResponseWriter.errorResponse(500, error)
  }
}
