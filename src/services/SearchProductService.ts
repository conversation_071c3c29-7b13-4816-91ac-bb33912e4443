import { ApiResponse } from '@opensearch-project/opensearch'
import { SearchResponse } from '@opensearch-project/opensearch/api/types'

import { SearchProductReq, SearchProductRes } from '../dto/product-search'
import { ProductSearchConverter } from '../converters/ProductSearchConverter'
import { opensearchClient } from '../common/aws-sdk-clients/opensearch'
import { MerchantIdPathParameter } from '../dto/common'

export class SearchProductService {
  static async searchProducts(
    pathReq: MerchantIdPathParameter,
    filter: SearchProductReq,
  ): Promise<SearchProductRes> {
    const params = ProductSearchConverter.toSearchRequest(pathReq, filter)

    const response: ApiResponse<SearchResponse> = await opensearchClient.search(params)

    return ProductSearchConverter.toSearchResponse(response.body)
  }
}
