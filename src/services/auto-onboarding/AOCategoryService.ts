import dayjs from 'dayjs'
import { AOCategoryRepository } from '../../repositories/auto-onboarding/AOCategoryRepository'
import { v4 as uuidV4 } from 'uuid'
import { EDomain } from '../../models/common/enums'
import {
  TCreateAOCategoryContract,
  TAOCategoryContract,
} from '../../models/contracts/auto-onboarding/Category/CategoryContract'

export class AOCategoryService {
  private companyId: string
  private repository: AOCategoryRepository

  constructor(companyId: string) {
    this.companyId = companyId
    this.repository = new AOCategoryRepository(companyId)
  }

  public async bulkCreate(contracts: TCreateAOCategoryContract[]) {
    const fullContracts: TAOCategoryContract[] = contracts.map((contract) => ({
      ...contract,
      companyId: this.companyId,
      domain: EDomain.CATEGORY,
      createdAt: dayjs(),
      updatedAt: dayjs(),
      id: uuidV4(),
    }))
    return this.repository.bulkCreate(fullContracts)
  }

  public async getAll(): Promise<TAOCategoryContract[]> {
    return this.repository.getAll()
  }
}
