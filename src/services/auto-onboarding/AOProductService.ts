import dayjs from 'dayjs'
import { AOProductRepository } from '../../repositories/auto-onboarding/AOProductRepository'
import { v4 as uuidV4 } from 'uuid'
import { EDomain } from '../../models/common/enums'
import {
  TCreateAOProductContract,
  TAOProductContract,
} from '../../models/contracts/auto-onboarding/Product/ProductContract'

export class AOProductService {
  private companyId: string
  private repository: AOProductRepository

  constructor(companyId: string) {
    this.companyId = companyId
    this.repository = new AOProductRepository(companyId)
  }

  public async bulkCreate(contracts: TCreateAOProductContract[]) {
    const fullContracts: TAOProductContract[] = contracts.map((contract) => ({
      ...contract,
      companyId: this.companyId,
      domain: EDomain.PRODUCT,
      createdAt: dayjs(),
      updatedAt: dayjs(),
      id: uuidV4(),
    }))
    return this.repository.bulkCreate(fullContracts)
  }

  public async getByBatch(batchNumber: number): Promise<TAOProductContract[]> {
    return this.repository.getByBatch(batchNumber)
  }
}
