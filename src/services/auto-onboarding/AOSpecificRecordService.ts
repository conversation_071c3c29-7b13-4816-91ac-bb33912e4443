import dayjs from 'dayjs'
import {
  TAOSpecificRecordContract,
  TCreateAOSpecificRecordContract,
} from '../../models/schemas/auto-onboarding/AOSpecificRecord'
import { AOSpecificRecordRepository } from '../../repositories/auto-onboarding/AOSpecificRecordRepository'
import { EDomain } from '../../models/common/enums'

export class AOSpecificRecordService {
  private companyId: string
  private domain: EDomain
  private repository: AOSpecificRecordRepository

  constructor(companyId: string, domain: EDomain) {
    this.companyId = companyId
    this.domain = domain
    this.repository = new AOSpecificRecordRepository(companyId, domain)
  }

  public async bulkCreate(contracts: TCreateAOSpecificRecordContract[]) {
    const fullContracts: TAOSpecificRecordContract[] = contracts.map((contract) => ({
      ...contract,
      companyId: this.companyId,
      domain: this.domain,
      createdAt: dayjs(),
      updatedAt: dayjs(),
    }))
    return this.repository.bulkCreate(fullContracts)
  }

  public async getById(id: string): Promise<TAOSpecificRecordContract[]> {
    return this.repository.getById(id)
  }
}
