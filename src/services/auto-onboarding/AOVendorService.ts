import dayjs from 'dayjs'
import { AOVendorRepository } from '../../repositories/auto-onboarding/AOVendorRepository'
import { v4 as uuidV4 } from 'uuid'
import { EDomain } from '../../models/common/enums'
import {
  TCreateAOVendorContract,
  TAOVendorContract,
} from '../../models/contracts/auto-onboarding/Vendor/VendorContract'

export class AOVendorService {
  private companyId: string
  private repository: AOVendorRepository

  constructor(companyId: string) {
    this.companyId = companyId
    this.repository = new AOVendorRepository(companyId)
  }

  public async bulkCreate(contracts: TCreateAOVendorContract[]) {
    const fullContracts: TAOVendorContract[] = contracts.map((contract) => ({
      ...contract,
      companyId: this.companyId,
      domain: EDomain.VENDOR,
      createdAt: dayjs(),
      updatedAt: dayjs(),
      id: uuidV4(),
    }))
    return this.repository.bulkCreate(fullContracts)
  }

  public async getAll(): Promise<TAOVendorContract[]> {
    return this.repository.getAll()
  }
}
