import dayjs from 'dayjs'
import { AOBrandRepository } from '../../repositories/auto-onboarding/AOBrandRepository'
import { v4 as uuidV4 } from 'uuid'
import { EDomain } from '../../models/common/enums'
import {
  TCreateAOBrandContract,
  TAOBrandContract,
} from '../../models/contracts/auto-onboarding/Brand/BrandContract'

export class AOBrandService {
  private companyId: string
  private repository: AOBrandRepository

  constructor(companyId: string) {
    this.companyId = companyId
    this.repository = new AOBrandRepository(companyId)
  }

  public async bulkCreate(contracts: TCreateAOBrandContract[]) {
    const fullContracts: TAOBrandContract[] = contracts.map((contract) => ({
      ...contract,
      companyId: this.companyId,
      domain: EDomain.BRAND,
      createdAt: dayjs(),
      updatedAt: dayjs(),
      id: uuidV4(),
    }))
    return this.repository.bulkCreate(fullContracts)
  }

  public async getAll(): Promise<TAOBrandContract[]> {
    return this.repository.getAll()
  }
}
