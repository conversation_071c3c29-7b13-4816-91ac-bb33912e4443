import { productPriceNotFoundError } from '../common/error'
import { MerchantIdPathParameter } from '../dto/common'
import { CommonConverter } from '../converters/CommonConverter'
import { ProductPriceIdPathReq, ProductPriceRes, UpdateProductPriceReq } from '../dto/product-price'
import { ProductPrice } from '../models/schemas/ProductPrice'
import { ProductPriceConverter } from '../converters/ProductPriceConverter'
import { generateProductPricePk } from '../utils/product-price'
import { InfoType } from '../models/common/enums'
import { AwsEventPublisher } from '../events/utilities'
import { ResourceType } from '../serializers/enums'
import { EventDetailType } from '../models/events/enums'

export class ProductPriceService {
  static async createProductPrice(pathReq: MerchantIdPathParameter, req: UpdateProductPriceReq) {
    const entity = ProductPriceConverter.toEntityCreate(pathReq, req)

    const productPrice = new ProductPrice(entity)
    await productPrice.save()

    const createdProductPrices = await CommonConverter.toDto(productPrice, ProductPriceRes)
    await AwsEventPublisher.publish(
      createdProductPrices,
      ResourceType.PRODUCT_PRICE_EVENT,
      EventDetailType.PRODUCT_PRICE_CREATED,
    )
    return createdProductPrices
  }

  static async updateProductPrice(pathReq: ProductPriceIdPathReq, req: UpdateProductPriceReq) {
    const pk = generateProductPricePk(pathReq.productId)
    const sk = InfoType.PRICE

    await this.checkProductPriceExists(pk, sk)

    const entity = ProductPriceConverter.toEntityUpdate(req)

    const updatedProductPriceEntity = await ProductPrice.update({ pk, sk }, entity)

    const updatedProductPrices = await CommonConverter.toDto(updatedProductPriceEntity, ProductPriceRes)

    await AwsEventPublisher.publish(
      updatedProductPrices,
      ResourceType.PRODUCT_PRICE_EVENT,
      EventDetailType.PRODUCT_PRICE_UPDATED,
    )
    return updatedProductPrices
  }

  static async checkProductPriceExists(pk: string, sk: string): Promise<void> {
    const existingProductPrice = await ProductPrice.get({
      pk,
      sk,
    })

    if (!existingProductPrice) {
      throw productPriceNotFoundError()
    }
  }

  static async getProductPrice(req: ProductPriceIdPathReq) {
    const pk = generateProductPricePk(req.productId)
    const sk = InfoType.PRICE
    const item = await ProductPrice.get({ pk, sk})
    if (!item) {
      throw productPriceNotFoundError()
    }
    return CommonConverter.toDto(item, ProductPriceRes)
  }
}
