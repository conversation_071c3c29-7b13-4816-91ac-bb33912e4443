import { CategoryConverter } from '../converters/CategoryConverter'
import { CategoryIdPathReq, CategoryRes, ChildCategoryRes, CreateCategoryReq, UpdateCategoryReq } from '../dto/category'
import { MerchantIdPathParameter } from '../dto/common'
import { Category, ChildCategory, ICategoryContract, IChildCategoryEntity } from '../models/schemas/Category'
import { categoryNotFoundError } from '../common/error'
import { generateCategoryPk } from '../utils/category'
import { CommonConverter } from '../converters/CommonConverter'
import { Product } from '../models/schemas/Product'
import { plainToInstance } from 'class-transformer'
import { AwsEventPublisher } from '../events/utilities'
import { ResourceType } from '../serializers/enums'
import { EventDetailType } from '../models/events/enums'

export class CategoryService {
  static async createCategory(pathReq: MerchantIdPathParameter, req: CreateCategoryReq) {
    const entity = CategoryConverter.toEntityCreate(pathReq, req)

    const categoryToSave = new Category(entity).save()

    const childEntities = req.childCategories.map((childCategory) => {
      return CategoryConverter.toChildEntityCreate(pathReq, req, { ...childCategory, parentCategoryId: entity.id })
    })

    const childCategoriesToSave = childEntities.map((childEntity) => {
      return new ChildCategory(childEntity).save()
    })

    await Promise.all([categoryToSave, ...childCategoriesToSave])
    const categoryContract = {
      ...entity,
      childCategories: childEntities.map((childEntity) => plainToInstance(ChildCategoryRes, childEntity))
    }
    const categoryCreatedResponse = CommonConverter.toDto(categoryContract, CategoryRes)

    await AwsEventPublisher.publish(
      categoryCreatedResponse,
      ResourceType.CATEGORY_EVENT,
      EventDetailType.CATEGORY_CREATED,
    )

    return categoryCreatedResponse
  }

  static async updateCategory(pathReq: CategoryIdPathReq, req: UpdateCategoryReq) {
    const pk = generateCategoryPk(pathReq.pos, pathReq.tenantId)
    const sk = pathReq.categoryId

    const existingCategory = await Category.get({ pk, sk })
    if (!existingCategory) {
      throw categoryNotFoundError()
    }

    const entity = CategoryConverter.toEntityUpdate({ ...req, id: sk })

    await Category.update({ pk, sk }, entity)

    const childEntities: Partial<IChildCategoryEntity>[] = []
    const deletedChildCategories: Partial<IChildCategoryEntity>[] = []
    if (req.childCategories && req.childCategories.length > 0) {
      const childEntityPromises: Promise<any>[] = []
      const existingChildCategories = await ChildCategory.query('pk').eq(pk).and().where('sk').beginsWith(`${pathReq.categoryId}#`).exec()

      for (const childCategory of req.childCategories) {
        if (childCategory.id) {
          const existingChildCategory = existingChildCategories.find((item) => item.id === childCategory.id)
          if (!existingChildCategory) {
            throw categoryNotFoundError()
          }
          const childEntity = CategoryConverter.toChildEntityUpdate(req, childCategory)
          childEntities.push({ ...existingChildCategory, ...childEntity })
          const updateChildCategory = ChildCategory.update({ pk, sk: `${sk}#${childCategory.id}` }, childEntity)
          childEntityPromises.push(updateChildCategory)
  
        } else {
          const childEntity = CategoryConverter.toChildEntityCreate(pathReq, req, { ...childCategory, parentCategoryId: sk })
          childEntities.push(childEntity)
          childEntityPromises.push(new ChildCategory(childEntity).save())
        }
      }
      await Promise.all(childEntityPromises)
      deletedChildCategories.push(...existingChildCategories.filter((item) => !childEntities.find((child) => child.id === item.id)))
      const deletedCategoryPromises = deletedChildCategories.map((deletedCategory) => ChildCategory.update({ pk, sk: `${sk}#${deletedCategory.id}` }, { deletedAt: new Date().toISOString() }))
      await Promise.all(deletedCategoryPromises)
    }

    const updateCategoryContract = {
      ...existingCategory,
      ...entity,
      childCategories: childEntities.map((childEntity) => plainToInstance(ChildCategoryRes, childEntity))
    }

    await AwsEventPublisher.publish(
      {
       ...entity,
       childCategories: [...childEntities, ...deletedChildCategories.map((deletedCategory) => { return {
        ...deletedCategory,
        deletedAt: new Date()
       }})] 
      },
      ResourceType.CATEGORY_EVENT,
      EventDetailType.CATEGORY_UPDATED,
    )


    return CommonConverter.toDto(updateCategoryContract, CategoryRes)
  }

  static async deleteCategory(pathReq: CategoryIdPathReq) {
    const pk = generateCategoryPk(pathReq.pos, pathReq.tenantId)
    const sk = pathReq.categoryId

    const existingCategories = await Category.query('pk').eq(pk).and().where('sk').beginsWith(`${sk}`).exec()
    if (existingCategories && existingCategories.length === 0) {
      throw categoryNotFoundError()
    }
    const deleteCategoryPromises = existingCategories.map((existingCategory) => 
      Product.update({ pk, sk: existingCategory.parentCategoryId ? `${sk}#${existingCategory.id}` : sk }, { deletedAt: new Date().toISOString() }))
    if (deleteCategoryPromises.length > 0) {
      await Promise.all(deleteCategoryPromises)
    }

    const categoryToDelete = {
      ...existingCategories[0],
      deletedAt: new Date().toISOString(),
      childCategories: existingCategories.filter((category) => category.parentCategoryId).map((item) => {
        return { ...item, deletedAt: new Date().toISOString() }
      })
    }
    await AwsEventPublisher.publish(
      categoryToDelete,
      ResourceType.CATEGORY_EVENT,
      EventDetailType.CATEGORY_DELETED,
    )
  }

  static async listCategories(req: MerchantIdPathParameter) {
    const pk = generateCategoryPk(req.pos, req.tenantId)

    const allCategories = await Category.query('pk').eq(pk).and().where('deletedAt').not().exists().exec()
    const categoriesMap = new Map<string, ICategoryContract>()

    allCategories.forEach((category) => {
      if (!category.parentCategoryId) {
        categoriesMap.set(category.id, { ...category, childCategories: [] })
      } else {
        const parentCategory = categoriesMap.get(category.parentCategoryId)

        if (parentCategory) {
          const childProductDto = CommonConverter.toDto({ ...category, parentCategoryId: parentCategory.id}, ChildCategoryRes)
          parentCategory.childCategories.push(childProductDto)
        }
      }
    })
    
    return [...categoriesMap].map(([id, item]) => CommonConverter.toDto(item, CategoryRes))
  }

  static async findCategory(req: CategoryIdPathReq) {
    const pk = generateCategoryPk(req.pos, req.tenantId)
    const sk = req.categoryId

    const allCategories = await Category.query('pk').eq(pk).and().where('sk').eq(sk).exec()
    if (!allCategories || (allCategories && allCategories.length === 0)) {
      throw categoryNotFoundError()
    }

    const parentCategory = allCategories.find((category) => !category.parentCategoryId)
    const responseObject = {
      ...parentCategory,
      childCategories: allCategories.filter((category) => category.parentCategoryId).map((item) => CommonConverter.toDto(item, ChildCategoryRes))
    }
    return CommonConverter.toDto(responseObject, CategoryRes)
  }
}

