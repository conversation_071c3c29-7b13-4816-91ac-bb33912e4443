import { ProductIdPathReq, ProductRes, UpdateProductReq } from '../dto/product'
import { InfoType } from '../models/common/enums'
import { Product } from '../models/schemas/Product'
import { ProductConverter } from '../converters/ProductConverter'
import { productNotFoundError } from '../common/error'
import { MerchantIdPathParameter } from '../dto/common'
import { CommonConverter } from '../converters/CommonConverter'
import { generateProductPk } from '../utils/product'

export class ProductService {
  static async createProduct(pathReq: MerchantIdPathParameter, req: UpdateProductReq) {
    const entity = ProductConverter.toEntityCreate(pathReq, req)

    const product = new Product(entity)
    await product.save()

    return CommonConverter.toDto(product, ProductRes)
  }

  static async updateProduct(id: string, req: UpdateProductReq) {
    const pk = generateProductPk(id)
    const sk = InfoType.INFO

    await this.checkProductExists(pk)

    const entity = ProductConverter.toEntityUpdate(req)

    const updatedProduct = await Product.update({ pk, sk }, entity)

    return CommonConverter.toDto(updatedProduct, ProductRes)
  }

  static async deleteProduct(id: string): Promise<void> {
    const pk = generateProductPk(id)
    const sk = InfoType.INFO

    await this.checkProductExists(pk)

    await Product.update({ pk, sk }, { deletedAt: new Date().toISOString() })
  }

  static async checkProductExists(pk: string): Promise<void> {
    const existingProduct = await Product.get({
      pk,
      sk: InfoType.INFO,
    })

    if (!existingProduct) {
      throw productNotFoundError()
    }
  }

  static async findProduct(req: ProductIdPathReq) {
    const pk = generateProductPk(req.productId)
    const sk = InfoType.INFO
    const item = await Product.get({ pk, sk })
    if (!item) {
      throw productNotFoundError()
    }
    return CommonConverter.toDto(item, ProductRes)
  }
}
