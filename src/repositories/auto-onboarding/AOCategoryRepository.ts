import { generateDBKeyV2 } from '../../common/db/dynamoose/utils'
import { AOCategoryDBModel, IAOCategoryKeys } from '../../models/schemas/auto-onboarding/AOCategory'
import { EDomain } from '../../models/common/enums'
import { chunkArray } from '../../utils/array'
import { PrimaryKeys } from '../../common/db/dynamoose/enums'
import dayjs from 'dayjs'
import { TAOCategoryContract } from '../../models/contracts/auto-onboarding/Category/CategoryContract'

type TPrimaryKeysParams = Pick<IAOCategoryKeys, 'companyId' | 'domain' | 'key'>

const getPrimaryKeys = ({ companyId, domain, key }: TPrimaryKeysParams) => {
  return {
    pk: generateDBKeyV2<IAOCategoryKeys>([{ companyId }]),
    sk: generateDBKeyV2<IAOCategoryKeys>([{ domain }, { uniqueRecord: undefined }, { key }]),
  }
}

const getPartialPrimaryKeys = ({
  companyId,
  domain,
  key,
}: Pick<TPrimaryKeysParams, 'companyId' | 'domain'> & Partial<Pick<TPrimaryKeysParams, 'key'>>) => {
  return {
    pk: generateDBKeyV2<IAOCategoryKeys>([{ companyId }]),
    sk: generateDBKeyV2<IAOCategoryKeys>([
      { domain },
      { uniqueRecord: undefined },
      ...(key ? [{ key }] : []),
    ]),
  }
}

export class AOCategoryRepository {
  private companyId: string

  constructor(companyId: string) {
    this.companyId = companyId
  }

  public async bulkCreate(contracts: TAOCategoryContract[]) {
    const entities = contracts.map((contract) => {
      const { key } = contract

      return {
        ...contract,
        ...getPrimaryKeys({ companyId: this.companyId, domain: EDomain.CATEGORY, key }),
        createdAt: contract.createdAt.toISOString(),
        updatedAt: contract.updatedAt.toISOString(),
      }
    })

    const batches = chunkArray(entities, 25)
    for (const batch of batches) {
      await AOCategoryDBModel.batchPut(batch)
    }
  }

  public async getAll(): Promise<TAOCategoryContract[]> {
    const { pk, sk } = getPartialPrimaryKeys({
      companyId: this.companyId,
      domain: EDomain.CATEGORY,
    })
    const entities = await AOCategoryDBModel.query(PrimaryKeys.PK)
      .eq(pk)
      .and()
      .where(PrimaryKeys.SK)
      .beginsWith(sk)
      .exec()

    return entities.map((entity) => ({
      id: entity.id,
      companyId: entity.companyId,
      domain: entity.domain,
      key: entity.key,
      name: entity.name,
      cannabisType: entity.cannabisType,
      complianceCategoryId: entity.complianceCategoryId,
      imageUrl: entity.imageUrl,
      isActive: entity.isActive,
      lowInventoryThreshold: entity.lowInventoryThreshold,
      unitType: entity.unitType,
      overrides: entity.overrides,
      specificRecordsIds: entity.specificRecordsIds,
      createdAt: dayjs(entity.createdAt),
      updatedAt: dayjs(entity.updatedAt),
    }))
  }
}
