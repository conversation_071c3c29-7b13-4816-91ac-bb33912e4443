import { generateDBKeyV2 } from '../../common/db/dynamoose/utils'
import { AOVendorDBModel, IAOVendorKeys } from '../../models/schemas/auto-onboarding/AOVendor'
import { EDomain } from '../../models/common/enums'
import { chunkArray } from '../../utils/array'
import { PrimaryKeys } from '../../common/db/dynamoose/enums'
import dayjs from 'dayjs'
import { TAOVendorContract } from '../../models/contracts/auto-onboarding/Vendor/VendorContract'

type TPrimaryKeysParams = Pick<IAOVendorKeys, 'companyId' | 'domain' | 'key'>

const getPrimaryKeys = ({ companyId, domain, key }: TPrimaryKeysParams) => {
  return {
    pk: generateDBKeyV2<IAOVendorKeys>([{ companyId }]),
    sk: generateDBKeyV2<IAOVendorKeys>([{ domain }, { uniqueRecord: undefined }, { key }]),
  }
}

const getPartialPrimaryKeys = ({
  companyId,
  domain,
  key,
}: Pick<TPrimaryKeysParams, 'companyId' | 'domain'> & Partial<Pick<TPrimaryKeysParams, 'key'>>) => {
  return {
    pk: generateDBKeyV2<IAOVendorKeys>([{ companyId }]),
    sk: generateDBKeyV2<IAOVendorKeys>([
      { domain },
      { uniqueRecord: undefined },
      ...(key ? [{ key }] : []),
    ]),
  }
}

export class AOVendorRepository {
  private companyId: string

  constructor(companyId: string) {
    this.companyId = companyId
  }

  public async bulkCreate(contracts: TAOVendorContract[]) {
    const entities = contracts.map((contract) => {
      const { key } = contract

      return {
        ...contract,
        ...getPrimaryKeys({ companyId: this.companyId, domain: EDomain.VENDOR, key }),
        createdAt: contract.createdAt.toISOString(),
        updatedAt: contract.updatedAt.toISOString(),
      }
    })

    const batches = chunkArray(entities, 25)
    for (const batch of batches) {
      await AOVendorDBModel.batchPut(batch)
    }
  }

  public async getAll(): Promise<TAOVendorContract[]> {
    const { pk, sk } = getPartialPrimaryKeys({
      companyId: this.companyId,
      domain: EDomain.VENDOR,
    })
    const entities = await AOVendorDBModel.query(PrimaryKeys.PK)
      .eq(pk)
      .and()
      .where(PrimaryKeys.SK)
      .beginsWith(sk)
      .exec()

    return entities.map((entity) => ({
      id: entity.id,
      companyId: entity.companyId,
      domain: entity.domain,
      key: entity.key,
      name: entity.name,
      addressCity: entity.addressCity,
      addressState: entity.addressState,
      addressStreet: entity.addressStreet,
      addressZip: entity.addressZip,
      contractFirstName: entity.contractFirstName,
      contractLastName: entity.contractLastName,
      description: entity.description,
      email: entity.email,
      fax: entity.fax,
      license: entity.license,
      licenseExpiration: entity.licenseExpiration,
      phone: entity.phone,
      website: entity.website,
      specificRecordsIds: entity.specificRecordsIds,
      createdAt: dayjs(entity.createdAt),
      updatedAt: dayjs(entity.updatedAt),
    }))
  }
}
