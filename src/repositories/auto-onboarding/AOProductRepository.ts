import { generateDBKeyV2 } from '../../common/db/dynamoose/utils'
import { AOProductDBModel, IAOProductKeys } from '../../models/schemas/auto-onboarding/AOProduct'
import { EDomain } from '../../models/common/enums'
import { chunkArray } from '../../utils/array'
import { PrimaryKeys } from '../../common/db/dynamoose/enums'
import dayjs from 'dayjs'
import { TAOProductContract } from '../../models/contracts/auto-onboarding/Product/ProductContract'

type TPrimaryKeysParams = Pick<IAOProductKeys, 'companyId' | 'domain' | 'batch' | 'key'>

const getPrimaryKeys = ({ companyId, domain, batch, key }: TPrimaryKeysParams) => {
  return {
    pk: generateDBKeyV2<IAOProductKeys>([{ companyId }]),
    sk: generateDBKeyV2<IAOProductKeys>([
      { domain },
      { uniqueRecord: undefined },
      { batch },
      { key },
    ]),
  }
}

const getPartialPrimaryKeys = ({
  companyId,
  domain,
  batch,
  key,
}: Pick<TPrimaryKeysParams, 'companyId' | 'domain'> &
  Partial<Pick<TPrimaryKeysParams, 'batch' | 'key'>>) => {
  return {
    pk: generateDBKeyV2<IAOProductKeys>([{ companyId }]),
    sk: generateDBKeyV2<IAOProductKeys>([
      { domain },
      { uniqueRecord: undefined },
      ...(batch ? [{ batch }] : []),
      ...(key ? [{ key }] : []),
    ]),
  }
}

export class AOProductRepository {
  private companyId: string

  constructor(companyId: string) {
    this.companyId = companyId
  }

  public async bulkCreate(contracts: TAOProductContract[]) {
    const BATCH_SIZE = 1000 // TODO: Update after testing
    const contractBatches = chunkArray(contracts, BATCH_SIZE)

    let batchIds: number[] = []
    let currentBatchId = 1

    for (const batchContracts of contractBatches) {
      const entities = batchContracts.map((contract, index) => {
        const { key } = contract

        return {
          ...contract,
          ...getPrimaryKeys({
            companyId: this.companyId,
            domain: EDomain.PRODUCT,
            batch: currentBatchId.toString(),
            key,
          }),
          createdAt: contract.createdAt.toISOString(),
          updatedAt: contract.updatedAt.toISOString(),
        }
      })

      const insertBatches = chunkArray(entities, 25)
      for (const batch of insertBatches) {
        await AOProductDBModel.batchPut(batch)
      }

      batchIds.push(currentBatchId)
      currentBatchId += 1
    }

    return batchIds
  }

  public async getByBatch(batchNumber: number): Promise<TAOProductContract[]> {
    const { pk, sk } = getPartialPrimaryKeys({
      companyId: this.companyId,
      domain: EDomain.PRODUCT,
      batch: batchNumber.toString(),
    })
    const entities = await AOProductDBModel.query(PrimaryKeys.PK)
      .eq(pk)
      .and()
      .where(PrimaryKeys.SK)
      .beginsWith(sk)
      .exec()

    return entities.map((entity) => ({
      id: entity.id,
      companyId: entity.companyId,
      domain: entity.domain,
      key: entity.key,
      name: entity.name,
      description: entity.description,
      shopId: entity.shopId,
      brandName: entity.brandName,
      isActive: entity.isActive,
      isWeedmapsEnabled: entity.isWeedmapsEnabled,
      weightPerUnit: entity.weightPerUnit,
      gramType: entity.gramType,
      weight: entity.weight,
      flowerType: entity.flowerType,
      imageUrls: entity.imageUrls,
      categoryName: entity.categoryName,
      saleType: entity.saleType,
      retailPrice: entity.retailPrice,
      wholesaleCost: entity.wholesaleCost,
      isAvailableOnline: entity.isAvailableOnline,
      sku: entity.sku,
      tags: entity.tags,
      vendorName: entity.vendorName,
      secondaryVendorNames: entity.secondaryVendorNames,
      overrides: entity.overrides.map((override) => ({
        shopId: override.shopId,
        sku: override.sku,
        isActive: override.isActive,
        retailPrice: override.retailPrice,
        wholesaleCost: override.wholesaleCost,
        tags: override.tags,
      })),
      specificRecordsIds: entity.specificRecordsIds,
      createdAt: dayjs(entity.createdAt),
      updatedAt: dayjs(entity.updatedAt),
    }))
  }
}
