import { generateDBKeyV2 } from '../../common/db/dynamoose/utils'
import { AOBrandDBModel, IAOBrandKeys } from '../../models/schemas/auto-onboarding/AOBrand'
import { EDomain } from '../../models/common/enums'
import { chunkArray } from '../../utils/array'
import { PrimaryKeys } from '../../common/db/dynamoose/enums'
import dayjs from 'dayjs'
import { TAOBrandContract } from '../../models/contracts/auto-onboarding/Brand/BrandContract'

type TPrimaryKeysParams = Pick<IAOBrandKeys, 'companyId' | 'domain' | 'key'>

const getPrimaryKeys = ({ companyId, domain, key }: TPrimaryKeysParams) => {
  return {
    pk: generateDBKeyV2<IAOBrandKeys>([{ companyId }]),
    sk: generateDBKeyV2<IAOBrandKeys>([{ domain }, { uniqueRecord: undefined }, { key }]),
  }
}

const getPartialPrimaryKeys = ({
  companyId,
  domain,
  key,
}: Pick<TPrimaryKeysParams, 'companyId' | 'domain'> & Partial<Pick<TPrimaryKeysParams, 'key'>>) => {
  return {
    pk: generateDBKeyV2<IAOBrandKeys>([{ companyId }]),
    sk: generateDBKeyV2<IAOBrandKeys>([
      { domain },
      { uniqueRecord: undefined },
      ...(key ? [{ key }] : []),
    ]),
  }
}

export class AOBrandRepository {
  private companyId: string

  constructor(companyId: string) {
    this.companyId = companyId
  }

  public async bulkCreate(contracts: TAOBrandContract[]) {
    const entities = contracts.map((contract) => {
      const { key } = contract

      return {
        ...contract,
        ...getPrimaryKeys({ companyId: this.companyId, domain: EDomain.BRAND, key }),
        createdAt: contract.createdAt.toISOString(),
        updatedAt: contract.updatedAt.toISOString(),
      }
    })

    const batches = chunkArray(entities, 25)
    for (const batch of batches) {
      await AOBrandDBModel.batchPut(batch)
    }
  }

  public async getAll(): Promise<TAOBrandContract[]> {
    const { pk, sk } = getPartialPrimaryKeys({
      companyId: this.companyId,
      domain: EDomain.BRAND,
    })
    const entities = await AOBrandDBModel.query(PrimaryKeys.PK)
      .eq(pk)
      .and()
      .where(PrimaryKeys.SK)
      .beginsWith(sk)
      .exec()

    return entities.map((entity) => ({
      id: entity.id,
      companyId: entity.companyId,
      sourceMerchantId: entity.sourceMerchantId,
      domain: entity.domain,
      key: entity.key,
      name: entity.name,
      address: entity.address,
      email: entity.email,
      phoneNumber: entity.phoneNumber,
      website: entity.website,
      specificRecordsIds: entity.specificRecordsIds,
      createdAt: dayjs(entity.createdAt),
      updatedAt: dayjs(entity.updatedAt),
    }))
  }
}
