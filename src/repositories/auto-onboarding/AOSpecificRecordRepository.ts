import { generateDBKeyV2 } from '../../common/db/dynamoose/utils'
import {
  AOSpecificRecordDBModel,
  TAOSpecificRecordContract,
  IAOSpecificRecordKeys,
} from '../../models/schemas/auto-onboarding/AOSpecificRecord'
import { EDomain } from '../../models/common/enums'
import { chunkArray } from '../../utils/array'
import { PrimaryKeys } from '../../common/db/dynamoose/enums'
import dayjs from 'dayjs'

type TPrimaryKeysParams = Pick<IAOSpecificRecordKeys, 'companyId' | 'domain' | 'id'>

const getPrimaryKeys = ({ companyId, domain, id }: TPrimaryKeysParams) => {
  return {
    pk: generateDBKeyV2<IAOSpecificRecordKeys>([{ companyId }]),
    sk: generateDBKeyV2<IAOSpecificRecordKeys>([{ domain }, { specificRecord: undefined }, { id }]),
  }
}

export class AOSpecificRecordRepository {
  private companyId: string
  private domain: EDomain

  constructor(companyId: string, domain: EDomain) {
    this.companyId = companyId
    this.domain = domain
  }

  public async bulkCreate(contracts: TAOSpecificRecordContract[]) {
    const entities = contracts.map((contract) => {
      const { id } = contract

      return {
        ...contract,
        ...getPrimaryKeys({ companyId: this.companyId, domain: this.domain, id }),
        createdAt: contract.createdAt.toISOString(),
        updatedAt: contract.updatedAt.toISOString(),
      }
    })

    const batches = chunkArray(entities, 25)
    for (const batch of batches) {
      await AOSpecificRecordDBModel.batchPut(batch)
    }
  }

  public async getById(id: string): Promise<TAOSpecificRecordContract[]> {
    const { pk, sk } = getPrimaryKeys({
      companyId: this.companyId,
      domain: this.domain,
      id,
    })

    const entities = await AOSpecificRecordDBModel.query(PrimaryKeys.PK)
      .eq(pk)
      .and()
      .where(PrimaryKeys.SK)
      .eq(sk)
      .exec()

    return entities.map((entity) => ({
      id: entity.id,
      key: entity.key,
      targetId: entity.targetId,
      companyId: entity.companyId,
      domain: entity.domain,
      createdAt: dayjs(entity.createdAt),
      updatedAt: dayjs(entity.updatedAt),
    }))
  }
}
