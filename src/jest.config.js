// eslint-disable-next-line @typescript-eslint/no-var-requires
const { defaults: tsjPreset } = require('ts-jest/presets')

module.exports = {
  ...tsjPreset,

  rootDir: './',

  transform: tsjPreset.transform,

  globals: {
    'ts-jest': {
      babelConfig: true,
    },
  },

  setupFiles: ['<rootDir>/jest.set-envs.ts'],

  setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'],

  testMatch: ['<rootDir>/tests/**/*.test.{ts,tsx}'],

  reporters: [['summary', { summaryThreshold: 0 }]],
  collectCoverage: false,
  coverageReporters: ['text', 'html'],
  coverageDirectory: '<rootDir>/coverage',
  coveragePathIgnorePatterns: ['/node_modules/'],
  testTimeout: 10000,
  testEnvironment: 'node',
}
