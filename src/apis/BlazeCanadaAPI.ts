import axios, { AxiosError, AxiosInstance } from 'axios'
import { stageValue } from '../utils/stage'
import {
  TCreateBlazeCanadaCategory,
  TCreateBlazeCanadaCategoryResponse,
  TGetBlazeCanadaCategoryResponse,
} from '../models/apis/blaze-canada/Category'
import {
  TCreateBlazeCanadaVendor,
  TCreateBlazeCanadaVendorResponse,
} from '../models/apis/blaze-canada/Vendor'
import {
  TCreateBlazeCanadaSupplier,
  TCreateBlazeCanadaSupplierResponse,
} from '../models/apis/blaze-canada/Supplier'
import {
  TCreateBlazeCanadaProductOverridesRequestBody,
  TCreateBlazeCanadaProductRequestBody,
  TCreateBlazeCanadaProductResponse,
} from '../models/apis/blaze-canada/Product'
import { EPOSProvider } from '../models/common/enums'
import { logger } from '../common/powertools/logger'

const baseURL = stageValue({
  sandbox: 'https://sandbox-core-api.getgreenline.co',
  staging: 'https://core-staging-api.getgreenline.co',
  prod: 'https://api.getgreenline.co',
})

export const BlazeCanadaAPIEndpoints = {
  CATEGORIES: (companyId: string) => `/api/v1/external/companies/${companyId}/categories`,
  VENDORS: (companyId: string) => `/api/v1/external/companies/${companyId}/vendors`,
  SUPPLIERS: (companyId: string) => `/api/v1/external/companies/${companyId}/suppliers`,
  PRODUCTS_BULK: (companyId: string) => `/api/v1/external/companies/${companyId}/products/bulk`,
  PRODUCTS_DETAILS_OVERRIDES: (companyId: string) =>
    `/api/v1/external/companies/${companyId}/products/details-overrides`,
  PRODUCTS_PRICES: (companyId: string) => `/api/v1/external/companies/${companyId}/products/prices`,
}

export class BlazeCanadaAPI {
  private axiosInstance: AxiosInstance

  constructor(apiKey: string, posProvider: EPOSProvider) {
    this.axiosInstance = axios.create({
      baseURL,
      headers: {
        'Content-Type': 'application/json',
        'api-key': apiKey,
        'x-pos-provider': posProvider,
      },
    })

    this.axiosInstance.interceptors.request.use((config) => {
      logger.debug(`BlazeCanadaAPI request - ${config.method} ${config.url}`, {
        params: config.params,
        data: config.data,
        headers: config.headers,
      })
      return config
    })

    this.axiosInstance.interceptors.response.use(
      (response) => {
        logger.debug(`BlazeCanadaAPI response - [${response.status}] ${response.statusText}`, {
          response: {
            data: response.data,
            headers: response.headers,
          },
        })
        return response
      },
      (error) => {
        if (error instanceof AxiosError) {
          logger.error(
            `BlazeCanadaAPI error - [${error.response?.status ?? error.status}] ${error.message}`,
            {
              response: {
                data: error.response?.data,
                headers: error.response?.headers,
              },
            },
          )
          return Promise.reject(error)
        }

        logger.error('BlazeCanadaAPI unknown error', {
          error: error instanceof Error ? error.message : 'Unknown error',
        })
        return Promise.reject(error)
      },
    )
  }

  public async getCategories(companyId: string) {
    const response = await this.axiosInstance.get<TGetBlazeCanadaCategoryResponse>(
      BlazeCanadaAPIEndpoints.CATEGORIES(companyId),
    )

    return response.data
  }

  public async createCategory(
    companyId: string,
    contract: TCreateBlazeCanadaCategory,
  ): Promise<TCreateBlazeCanadaCategoryResponse> {
    const response = await this.axiosInstance.post<TCreateBlazeCanadaCategoryResponse>(
      BlazeCanadaAPIEndpoints.CATEGORIES(companyId),
      contract,
    )

    return response.data
  }

  public async createVendor(
    companyId: string,
    contract: TCreateBlazeCanadaVendor,
  ): Promise<TCreateBlazeCanadaVendorResponse> {
    const response = await this.axiosInstance.post<TCreateBlazeCanadaVendorResponse>(
      BlazeCanadaAPIEndpoints.VENDORS(companyId),
      contract,
    )

    return response.data
  }

  public async createSupplier(
    companyId: string,
    contract: TCreateBlazeCanadaSupplier,
  ): Promise<TCreateBlazeCanadaSupplierResponse> {
    const response = await this.axiosInstance.post<TCreateBlazeCanadaSupplierResponse>(
      BlazeCanadaAPIEndpoints.SUPPLIERS(companyId),
      contract,
    )

    return response.data
  }

  public async bulkCreateProductsFromCSV(
    companyId: string,
    contract: TCreateBlazeCanadaProductRequestBody,
  ): Promise<TCreateBlazeCanadaProductResponse> {
    const response = await this.axiosInstance.post<TCreateBlazeCanadaProductResponse>(
      BlazeCanadaAPIEndpoints.PRODUCTS_BULK(companyId),
      contract,
    )

    return response.data
  }

  public async bulkCreateProductsDetailsFromCSV(
    companyId: string,
    contract: TCreateBlazeCanadaProductOverridesRequestBody,
  ): Promise<TCreateBlazeCanadaProductResponse> {
    const response = await this.axiosInstance.post<TCreateBlazeCanadaProductResponse>(
      BlazeCanadaAPIEndpoints.PRODUCTS_DETAILS_OVERRIDES(companyId),
      contract,
    )

    return response.data
  }

  public async bulkCreateProductsPricesFromCSV(
    companyId: string,
    contract: TCreateBlazeCanadaProductOverridesRequestBody,
  ): Promise<TCreateBlazeCanadaProductResponse> {
    const response = await this.axiosInstance.post<TCreateBlazeCanadaProductResponse>(
      BlazeCanadaAPIEndpoints.PRODUCTS_PRICES(companyId),
      contract,
    )

    return response.data
  }
}
