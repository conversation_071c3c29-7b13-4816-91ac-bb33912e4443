import 'reflect-metadata'
import * as dotenv from 'dotenv'
import path from 'path'
import { startReplServer } from 'ts-node-repl'

dotenv.config({
  path: path.resolve(__dirname, `./repl-configs/.env.repl`),
})

const targetDir = __dirname
const mountDirGlob = `${targetDir}/**/**/**/!(*.d|*.test|jest.*).ts`

// inspiration from https://sapandiwakar.in/rails-like-console-for-node-js-and-node-ts-2/
const { replServer } = startReplServer({
  verbose: true,
  replOptions: {
    prompt: 'app> ',
    useColors: true,
  },
  moduleMountOptions: {
    pattern: mountDirGlob,
    onError: (error) => {
      throw error
    },
  },
  watchOptions: {
    paths: mountDirGlob,
    options: {
      ignoreInitial: true, // We don't want resynth to take place for every single initial file change
    },
  },
})

replServer.setupHistory('./.repl_history', () => {
  return
})
