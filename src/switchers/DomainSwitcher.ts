import { BrandAdapter } from '../adapters/BrandAdapter'
import { CategoryAdapter } from '../adapters/CategoryAdapter'
import { ProductAdapter } from '../adapters/ProductAdapter'
import { VendorAdapter } from '../adapters/VendorAdapter'
import { DomainAdapter } from '../models/adapters/DomainAdapter'
import { EDomain } from '../models/common/enums'

export const getAdapter = (
  companyId: string,
  domain: string,
): DomainAdapter<unknown, unknown, never, boolean> => {
  switch (domain) {
    case EDomain.CATEGORY:
      return new CategoryAdapter(companyId)
    case EDomain.VENDOR:
      return new VendorAdapter(companyId)
    case EDomain.BRAND:
      return new BrandAdapter(companyId)
    case EDomain.PRODUCT:
      return new ProductAdapter(companyId)
    default:
      throw new Error(
        `Unsupported domain: '${domain}'. Expected one of: ${Object.values(EDomain).join(', ')}`,
      )
  }
}
