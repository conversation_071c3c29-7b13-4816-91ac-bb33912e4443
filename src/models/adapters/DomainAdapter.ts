import { BlazeCanadaAPI } from '../../apis/BlazeCanadaAPI'
import { TStage } from '../../utils/stage'
import { TDedupedRecord } from '../contracts/auto-onboarding/Overrides'

export type DomainAdapter<
  TExportRow,
  TContract,
  K extends keyof TExportRow & string,
  IsBulkDomain extends boolean,
> = {
  /** If the domain is a bulk import domain */
  isBulkDomain(): IsBulkDomain

  /** Validate + convert raw CSV rows */
  parse(rows: Record<string, string>[]): TExportRow[]

  /** Key used as unique identifier (usually just id) */
  getIdFromContract(record: TContract): string

  /** Field used to get the unique record by name (name -> canonical + hash) */
  getDedupeKeyFromContract(contract: TContract): string

  /** Build the S3 object key for this domain/company */
  csvKey(): string

  /** A key used to detect duplicates within the batch */
  dedupeKey(row: TExportRow): string

  /** Unify records with the same dedupe key */
  unifyMatchingRecords(
    rows: TExportRow[],
  ): [K] extends [never] ? TExportRow[] : TDedupedRecord<TExportRow, K>[]
} & (IsBulkDomain extends true
  ? {
      /** Bulk create the records in BLAZE Canada API from the service contract */
      bulkCreateRecordsInBlazeCanada(
        records: TContract[],
        {
          api,
          bucketName,
          stage,
          batchNumber,
        }: {
          api: BlazeCanadaAPI
          bucketName: string
          stage: TStage
          batchNumber: number
        },
      ): Promise<void>

      /** Get the unified records stored in the service DynamoDB */
      getSavedRecordsByBatch(batchNumber: number): Promise<TContract[]>

      /** Bulk creates the parsed/unified records into the service DynamoDB */
      saveRecords(
        records: [K] extends [never] ? TExportRow[] : TDedupedRecord<TExportRow, K>[],
      ): Promise<number[]>

      createRecordInBlazeCanada?: never
    }
  : {
      /** Create the record in BLAZE Canada API from the service contract */
      createRecordInBlazeCanada(api: BlazeCanadaAPI, record: TContract): Promise<string>

      /** Get the unified records stored in the service DynamoDB */
      getSavedRecords(): Promise<TContract[]>

      /** Bulk creates the parsed/unified records into the service DynamoDB */
      saveRecords(
        records: [K] extends [never] ? TExportRow[] : TDedupedRecord<TExportRow, K>[],
      ): Promise<void>

      bulkCreateRecordsInBlazeCanada?: never
    })
