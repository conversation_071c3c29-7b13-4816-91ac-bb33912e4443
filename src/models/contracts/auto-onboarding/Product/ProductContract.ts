import { Dayjs } from 'dayjs'
import { EDomain } from '../../../common/enums'
import { TProductExportRow } from './ProductExportRow'

interface ICoreAOProductContract {
  id: string
  companyId: string
  shopId: string
  name: string
  isActive: boolean
  brandName?: string
  weightPerUnit?: TProductExportRow['weightPerUnit']
  gramType?: TProductExportRow['customGramType']
  weight?: number
  description?: string
  flowerType?: string
  imageUrls?: string[]
  isWeedmapsEnabled?: boolean
  categoryName?: string
  saleType?: TProductExportRow['productSaleType']
  retailPrice?: number
  wholesaleCost?: number
  isAvailableOnline?: boolean
  sku?: string
  tags?: string[]
  vendorName?: string
  secondaryVendorNames?: string[]
  overrides: {
    shopId: string
    sku?: string
    isActive?: boolean
    retailPrice?: number
    wholesaleCost?: number
    tags?: string[]
  }[]
  key: string
  specificRecordsIds: string[]
}

export type TCreateAOProductContract = ICoreAOProductContract
export type TAOProductContract = ICoreAOProductContract & {
  id: string
  createdAt: Dayjs
  updatedAt: Dayjs
  companyId: string
  domain: EDomain
}
