import { z } from 'zod'
import { TDedupedRecord } from '../Overrides'
import { MongoObjectId } from '../../../common/zod'

const BrandExportRowSchema = z.object({
  id: MongoObjectId,
  name: z.string().min(1),
  companyId: MongoObjectId,
  phoneNumber: z.string().min(1).optional(),
  email: z.string().email().optional(),
  website: z.string().url().optional(),
  address: z.string().min(1).optional(),
})

const BrandExportSchema = BrandExportRowSchema.array().nonempty()

export type TBrandExportRow = z.infer<typeof BrandExportRowSchema>
export type TDedupedBrandExportRow = TDedupedRecord<TBrandExportRow, 'id'>
export type TBrandExport = z.infer<typeof BrandExportSchema>
export const BrandExportRowFields = Object.keys(
  BrandExportRowSchema.shape,
) as (keyof TBrandExportRow)[]

export const parseBrandExportRow = (data: unknown) => {
  return BrandExportRowSchema.parse(data)
}

export const parseBrandExport = (data: unknown): TBrandExport => {
  return BrandExportSchema.parse(data)
}
