import { Dayjs } from 'dayjs'
import { EDomain } from '../../../common/enums'

interface ICoreAOBrandContract {
  key: string
  name: string
  sourceMerchantId: string
  website?: string
  phoneNumber?: string
  address?: string
  email?: string
  specificRecordsIds: string[]
}

export type TCreateAOBrandContract = ICoreAOBrandContract
export type TAOBrandContract = ICoreAOBrandContract & {
  id: string
  createdAt: Dayjs
  updatedAt: Dayjs
  companyId: string
  domain: EDomain
}
