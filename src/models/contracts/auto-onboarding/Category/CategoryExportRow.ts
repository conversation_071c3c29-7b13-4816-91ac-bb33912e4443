import { z } from 'zod'
import { TDedupedRecord } from '../Overrides'
import { MongoObjectId } from '../../../common/zod'

const ValidUnitTypes = z.enum(['grams', 'units'])

const ValidCannabisTypes = z.enum([
  'NONE',
  'DEFAULT',
  'NON_CANNABIS',
  'CBD',
  'CBD_CANNABIS',
  'CBD_CANNABIS_FLOWER',
  'CONCENTRATE',
  'NON_CONCENTRATE',
  'PLANT',
  'EDIBLE',
  'EXTRACT',
  'DRY_FLOWER',
  'KIEF',
  'DRY_LEAF',
  'LIQUID',
  'SUPPOSITORY',
  'TINCTURE',
  'TOPICAL',
  'OIL',
  'SEEDS',
  'THC',
  'LIVE_PLANT',
  'IMMATURE_PLANT',
  'CLONE_CUTTING',
  'CLONE_TISSUE_CULTURE',
  'PRE_ROLL_FLOWER',
  'PRE_ROLL_LEAF',
  'PRE_ROLL_INFUSED',
  'SEEDS_EA',
  'PRE_ROLL',
  'PRE_ROLL_CBD',
  'CAPSULE',
  'COMBINED',
  'LOZENGES',
  'EXTRACT_INHALED',
  'BEVERAGES',
  'GEL_BASED_FOOD',
  'ORAL_LIQUID',
  'RESIN',
  'SOLID_CHOCOLATE',
  'WATER_SOLUBLE_EDIBLE',
  'WAX',
])

const CategoryExportRowSchema = z.object({
  id: MongoObjectId,
  imageUrl: z.string().url().optional(),
  shopId: MongoObjectId,
  name: z.string().min(1),
  isActive: z.coerce.boolean(),
  isCannabis: z.coerce.boolean(),
  unitType: ValidUnitTypes,
  cannabisType: ValidCannabisTypes.optional(),
  complianceCategoryId: z.string().uuid().optional(),
  lowInventoryThreshold: z.coerce.number().optional(),
})

const CategoryExportSchema = CategoryExportRowSchema.array().nonempty()

export type TCategoryExportRow = z.infer<typeof CategoryExportRowSchema>
export type TDedupedCategoryExportRow = TDedupedRecord<TCategoryExportRow, 'shopId'>
export type TCategoryExport = z.infer<typeof CategoryExportSchema>
export const CategoryExportRowFields = Object.keys(
  CategoryExportRowSchema.shape,
) as (keyof TCategoryExportRow)[]

export const parseCategoryExportRow = (data: unknown) => {
  return CategoryExportRowSchema.parse(data)
}

export const parseCategoryExport = (data: unknown): TCategoryExport => {
  return CategoryExportSchema.parse(data)
}
