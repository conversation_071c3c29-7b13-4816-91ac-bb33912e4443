import { Dayjs } from 'dayjs'
import { EDomain } from '../../../common/enums'
import { TDedupedCategoryExportRow } from './CategoryExportRow'

interface ICoreAOCategoryContract {
  key: string
  name: string
  isActive: boolean
  unitType: TDedupedCategoryExportRow['unitType']
  imageUrl?: string
  cannabisType?: TDedupedCategoryExportRow['cannabisType']
  complianceCategoryId?: string
  lowInventoryThreshold?: number
  overrides: {
    shopId: string
    lowInventoryThreshold?: number
  }[]
  specificRecordsIds: string[]
}

export type TCreateAOCategoryContract = ICoreAOCategoryContract
export type TAOCategoryContract = ICoreAOCategoryContract & {
  id: string
  createdAt: Dayjs
  updatedAt: Dayjs
  companyId: string
  domain: EDomain
}
