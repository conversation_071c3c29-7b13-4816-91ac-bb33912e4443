import { Dayjs } from 'dayjs'
import { EDomain } from '../../../common/enums'

interface ICoreAOVendorContract {
  key: string
  name: string
  license?: string
  licenseExpiration?: string
  contractFirstName?: string
  contractLastName?: string
  phone?: string
  fax?: string
  email?: string
  website?: string
  addressStreet?: string
  addressCity?: string
  addressState?: string
  addressZip?: string
  description?: string
  specificRecordsIds: string[]
}

export type TCreateAOVendorContract = ICoreAOVendorContract
export type TAOVendorContract = ICoreAOVendorContract & {
  id: string
  createdAt: Dayjs
  updatedAt: Dayjs
  companyId: string
  domain: EDomain
}
