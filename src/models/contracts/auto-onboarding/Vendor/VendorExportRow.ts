import { z } from 'zod'
import { TDedupedRecord } from '../Overrides'
import dayjs from 'dayjs'
import { MongoObjectId } from '../../../common/zod'

const VendorExportRowSchema = z.object({
  id: MongoObjectId,
  name: z.string().min(1),
  license: z.string().min(1).optional(),
  licenseExpiration: z
    .string()
    .refine((date) => dayjs(date).isValid())
    .optional(),
  contractFirstName: z.string().min(1).optional(),
  contractLastName: z.string().min(1).optional(),
  phone: z.string().min(1).optional(),
  fax: z.string().min(1).optional(),
  email: z.string().email().optional(),
  website: z.string().url().optional(),
  addressStreet: z.string().min(1).optional(),
  addressCity: z.string().min(1).optional(),
  addressState: z.string().min(1).optional(),
  addressZip: z.string().min(1).optional(),
  description: z.string().min(1).optional(),
})

const VendorExportSchema = VendorExportRowSchema.array().nonempty()

export type TVendorExportRow = z.infer<typeof VendorExportRowSchema>
export type TDedupedVendorExportRow = TDedupedRecord<TVendorExportRow, 'id'>
export type TVendorExport = z.infer<typeof VendorExportSchema>
export const VendorExportRowFields = Object.keys(
  VendorExportRowSchema.shape,
) as (keyof TVendorExportRow)[]

export const parseVendorExportRow = (data: unknown) => {
  return VendorExportRowSchema.parse(data)
}

export const parseVendorExport = (data: unknown): TVendorExport => {
  return VendorExportSchema.parse(data)
}
