import { EVENT_SUFFIX } from './constants'

export enum EventTopic {
  PRODUCT = 'product',
  CATEGORY = 'category',
  PRODUCT_PRICE = 'product_price',
  INVENTORY_INFO = 'inventory_info',
}

export enum EventType {
  CREATED = 'created',
  UPDATED = 'updated',
  DELETED = 'deleted',
}

export enum EventDetailType {
  PRODUCT_CREATED = `${EventTopic.PRODUCT}.${EventType.CREATED}`,
  PRODUCT_UPDATED = `${EventTopic.PRODUCT}.${EventType.UPDATED}`,
  PRODUCT_DELETED = `${EventTopic.PRODUCT}.${EventType.DELETED}`,
  CATEGORY_CREATED = `${EventTopic.CATEGORY}.${EventType.CREATED}`,
  CATEGORY_UPDATED = `${EventTopic.CATEGORY}.${EventType.UPDATED}`,
  CATEGORY_DELETED = `${EventTopic.CATEGORY}.${EventType.DELETED}`,
  PRODUCT_PRICE_CREATED = `${EventTopic.PRODUCT_PRICE}.${EventType.CREATED}`,
  PRODUCT_PRICE_UPDATED = `${EventTopic.PRODUCT_PRICE}.${EventType.UPDATED}`,
  PRODUCT_PRICE_DELETED = `${EventTopic.PRODUCT_PRICE}.${EventType.DELETED}`,
  INVENTORY_INFO_UPDATED = `${EventTopic.INVENTORY_INFO}.${EventType.UPDATED}`,
}

export enum EventRequestType {
  PRODUCT_EVENTS = `${EventTopic.PRODUCT}${EVENT_SUFFIX}`,
}
