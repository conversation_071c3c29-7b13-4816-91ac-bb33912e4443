import { z } from 'zod'
import { EDomain } from '../../common/enums'
import { jsonPreprocessor } from '../../common/zod'

const StartSyncEventSchema = z.object({
  pathParameters: z.object({
    companyId: z
      .string()
      .min(1)
      .regex(/^[0-9]*$/),
  }),
  body: z.preprocess(
    jsonPreprocessor,
    z.object({
      domains: z.nativeEnum(EDomain).array().min(1),
      force: z.boolean().nullish(),
    }),
  ),
})

export type TStartSyncEvent = z.infer<typeof StartSyncEventSchema>

export const parseStartSyncEvent = (data: unknown) => {
  return StartSyncEventSchema.parse(data)
}
