import { z } from 'zod'
import { EDomain } from '../../common/enums'

const ImportBatchEventSchema = z.object({
  companyId: z
    .string()
    .min(1)
    .regex(/^[0-9]*$/),
  domain: z.nativeEnum(EDomain),
  executionDomains: z.nativeEnum(EDomain).array().min(1),
  batch: z.number().positive().optional(),
})

export type TImportBatchEvent = z.infer<typeof ImportBatchEventSchema>

export const parseImportBatchEvent = (data: unknown) => {
  return ImportBatchEventSchema.parse(data)
}
