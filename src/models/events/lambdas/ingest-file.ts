import { z } from 'zod'
import { EDomain } from '../../common/enums'

const IngestFileEventSchema = z.object({
  companyId: z
    .string()
    .min(1)
    .regex(/^[0-9]*$/),
  domain: z.nativeEnum(EDomain),
  executionDomains: z.nativeEnum(EDomain).array().min(1),
})

export type TIngestFileEvent = z.infer<typeof IngestFileEventSchema>

export const parseIngestFileEvent = (data: unknown) => {
  return IngestFileEventSchema.parse(data)
}
