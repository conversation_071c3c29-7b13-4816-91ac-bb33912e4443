interface IVendorAddress {
  address?: string
  city?: string
  state?: string
  zip?: string
}

export interface TCreateBlazeCanadaVendor {
  companyId: number
  name: string
  license?: string
  licenseExpiration?: string
  contactFirstName?: string
  contactLastName?: string
  address?: IVendorAddress
  email?: string
  phone?: string
  fax?: string
  website?: string
  description?: string
  deleteDate?: string
}

export type TCreateBlazeCanadaVendorResponse = {
  id: string
  // Add more as needed
}
