/**
 * This is the same as the one found in @getgreenline/categories > CategoriesModelsV2
 * The reason it is replicated here is because the shared packages added way too many unnecessary peer dependencies
 * For example: 'mobx' and 'amazon-cognito-identity-js', which increased the build size and introduced complexity
 **/

export enum CategoryType {
  STANDARD = 'standard',
  SYSTEM_FEES = 'systemFees',
}

export enum MeasurementType {
  GRAMS = 'gram',
  UNIT = 'unit',
}

interface ICategoryLocationOverride {
  locationId: number
  categoryId: string
  lowInventoryThreshold: number | null
}

type LeaflyCategory =
  | 'Accessory'
  | 'Seeds'
  | 'Clone'
  | 'Flower'
  | 'Edible'
  | 'PreRoll'
  | 'Concentrate'
  | 'Cartridge'
  | 'Topical'
  | 'Other'

export type TCreateBlazeCanadaCategory = {
  name: string
  parentCategoryId?: string
  colorHex?: string
  complianceCategoryId?: number | null
  platformComplianceCategoryId?: string | null
  wooCommerceCategoryId?: number | null
  leaflyCategory?: LeaflyCategory | null
  categoryType?: CategoryType
  measurementType?: MeasurementType | null
  exitLabel?: string | null
  lowInventoryThreshold?: number | null
  cannabisCategory?: string | null
  cannabisType?: string | null
  isActive?: boolean
  imageUrl?: string | null
  categoryLocationOverrides?: ICategoryLocationOverride[]
}

export type TCreateBlazeCanadaCategoryResponse = {
  id: string
  // Add more as needed
}

export type TGetBlazeCanadaCategoryResponse = {
  id: string
  // Add more as needed
}[]
