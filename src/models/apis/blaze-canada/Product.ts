import { EPOSProvider } from '../../common/enums'

export enum EProductSellType {
  RECREATIONAL = 'recreational',
  MEDICINAL = 'medicinal',
  BOTH = 'both',
}

export enum EProductStrainType {
  SATIVA = 'Sativa',
  SATIVA_DOMINANT = 'Sativa Dominant',
  INDICA = 'Indica',
  INDICA_DOMINANT = 'Indica Dominant',
  HYBRID = 'Hybrid',
  CBD = 'CBD',
  BALANCED = 'Balanced',
  BLEND = 'Blend',
}

type TUnit = 'gram' | 'unit'

type TBooleanString = 'TRUE' | 'FALSE'

export interface TCreateBlazeCanadaProductCSVRow {
  /** ALWAYS EMPTY: Blaze Canada expects the id column to exist but all empty */
  ID: ''

  /** Stock‑keeping unit (barcode or custom code) */
  SKU?: string

  /** Public‑facing product name */
  Name: string

  /** Category label as it appears in Blaze (NOT the compliance category) */
  Category?: string

  /** Primary supplier’s display name. Pass `null` if not set. */
  Brand?: string | null

  /** Comma‑separated list of vendor names (secondary suppliers) */
  Vendor?: string

  /** Comma‑separated list of absolute image URLs */
  ['Image URL']?: string

  /** Comma‑separated list of product tags */
  Tags?: string

  /** Unit purchase price, **always** formatted as `"$<dollars.cents>"` (e.g. `"$4.99"`) */
  ['Wholesale Cost']?: string

  /** Unit retail price, formatted the same way as `purchasePrice` */
  ['Retail Price']?: string

  /** Long‑form product description (plain text or limited HTML) */
  Description?: string

  /** `"TRUE"` if the item can be purchased online, else `"FALSE"` */
  ['Available Online']?: TBooleanString | ''

  /** `"TRUE"` if the item is currently active in Blaze, else `"FALSE"` */
  ['Is Active']: TBooleanString

  /** Net cannabis weight **as a decimal string** (e.g. `"1.23"`) */
  ['Cannabis Weight']?: string

  /** Unit of measure: 'unit' | 'gram' */
  ['Weight Unit']?: TUnit

  /** Product strain type (balanced, sativa, indica, etc.) */
  ['Flower Type']?: EProductStrainType

  /** Allowed sell mode (`recreational`, `medicinal`, or `both`) */
  ['Sell Type']?: EProductSellType

  /** ISO‑8601 timestamp for when the product was first created */
  ['Created']?: string

  /** ISO‑8601 timestamp for the most recent product update */
  ['Updated']?: string
}

export interface TCreateBlazeCanadaProductDetailsOverrideCSVRow {
  ['ID']: string
  ['Product Name']: string
  ['Shop']: string
  ['SKU']: string
  ['Is Active']: TBooleanString
  ['Price']: string
  ['Wholesale Cost']: string
}

export interface TCreateBlazeCanadaProductPriceOverrideCSVRow {
  ['ID']: string
  ['Product Name']: string
  ['Shop']: string
  ['Price']: string
}

export interface TCreateBlazeCanadaProductRequestBody {
  bucketName: string
  bucketRegion: string
  fileName: string
  enableCreate: boolean
}

export interface TCreateBlazeCanadaProductOverridesRequestBody {
  bucketName: string
  bucketRegion: string
  fileName: string
}

export interface TCreateBlazeCanadaProductRequestHeader {
  ['x-pos-provider']: EPOSProvider
}

export type TCreateBlazeCanadaProductResponse = {
  id: string
  childProducts?: TCreateBlazeCanadaProductResponse[]
}
