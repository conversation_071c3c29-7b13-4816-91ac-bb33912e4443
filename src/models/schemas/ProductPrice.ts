import { dynamoose } from '../../common/db/dynamoose/dynamoose'
import {
  commonDynamooseSchemaSettings,
  commonModelOptions,
} from '../common/dynamoose'
import { buildModel } from '../common/utils'

export interface IProductRetailerPriceAttributes {
  retailerId: string
  unitPrice: number
}
export interface IProductPriceAttributes {
  unitPrice: number
  retailerPrices: IProductRetailerPriceAttributes[]
  companyProductId: string
}

export interface IProductPriceEntity extends IProductPriceAttributes {
  pk: string
  sk: string
  tenantId: string
  recordType: string
  deletedAt: string
  createdAt: string
  updatedAt: string
}

export const productPriceSchema = new dynamoose.Schema(
  {
    pk: {
      type: String,
      hashKey: true,
      required: true,
    },
    sk: {
      type: String,
      rangeKey: true,
      required: true,
    },
    tenantId: {
      type: String,
    },
    unitPrice: {
      type: Number,
      required: true,
    },
    companyProductId: {
      type: String,
      required: true,
    },
    retailerPrices: {
      type: Array,
      schema: [{
        type: Object,
        schema: {
          retailerId: String,
          unitPrice: Number,
        }
      }]
    },
    recordType: {
      type: String,
    },
    deletedAt: {
      type: String,
    },
  },
  {
    ...commonDynamooseSchemaSettings,
  },
)

export const ProductPrice = buildModel<IProductPriceEntity>(productPriceSchema, commonModelOptions)
