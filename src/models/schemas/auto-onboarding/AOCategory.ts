import { dynamoose } from '../../../common/db/dynamoose/dynamoose'
import { commonModelOptions, commonSchema, TSchemaDefinition } from '../../common/dynamoose'
import { buildModel } from '../../common/utils'
import { TAOCategoryContract } from '../../contracts/auto-onboarding/Category/CategoryContract'

export interface IAOCategoryKeys extends TAOCategoryContract {
  uniqueRecord: undefined
}

interface IAOCategoryEntity extends Omit<TAOCategoryContract, 'createdAt' | 'updatedAt'> {
  pk: string
  sk: string
  createdAt: string
  updatedAt: string
}

const schemaDefinition: TSchemaDefinition<IAOCategoryEntity> = {
  pk: commonSchema.pk,
  sk: commonSchema.sk,
  id: {
    type: String,
    required: true,
  },
  key: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  cannabisType: {
    type: String,
    required: false,
  },
  complianceCategoryId: {
    type: String,
    required: false,
  },
  imageUrl: {
    type: String,
    required: false,
  },
  isActive: {
    type: Boolean,
    required: true,
  },
  lowInventoryThreshold: {
    type: Number,
    required: false,
  },
  unitType: {
    type: String,
    required: true,
  },
  createdAt: {
    type: String,
    required: true,
  },
  updatedAt: {
    type: String,
    required: true,
  },
  companyId: {
    type: String,
    required: true,
  },
  domain: {
    type: String,
    required: true,
  },
  overrides: {
    type: Array,
    required: false,
    schema: [
      {
        type: Object,
        schema: {
          shopId: {
            type: String,
            required: true,
          },
          lowInventoryThreshold: {
            type: Number,
            required: false,
          },
        },
      },
    ],
  },
  specificRecordsIds: {
    type: Array,
    required: false,
    schema: [
      {
        type: String,
        required: false,
      },
    ],
  },
}

const aoCategorySchema = new dynamoose.Schema(schemaDefinition, {
  saveUnknown: false,
})

export const AOCategoryDBModel = buildModel<IAOCategoryEntity>(aoCategorySchema, commonModelOptions)
