import { Dayjs } from 'dayjs'
import { dynamoose } from '../../../common/db/dynamoose/dynamoose'
import { commonModelOptions, commonSchema, TSchemaDefinition } from '../../common/dynamoose'
import { EDomain } from '../../common/enums'
import { buildModel } from '../../common/utils'

interface ICoreAOSpecificRecordContract {
  id: string
  targetId: string
  key: string
}

export type TCreateAOSpecificRecordContract = ICoreAOSpecificRecordContract
export type TAOSpecificRecordContract = ICoreAOSpecificRecordContract & {
  createdAt: Dayjs
  updatedAt: Dayjs
  companyId: string
  domain: EDomain
}

export interface IAOSpecificRecordKeys extends TAOSpecificRecordContract {
  specificRecord: undefined
}

interface IAOSpecificRecordEntity
  extends Omit<TAOSpecificRecordContract, 'createdAt' | 'updatedAt'> {
  pk: string
  sk: string
  createdAt: string
  updatedAt: string
}

const schemaDefinition: TSchemaDefinition<IAOSpecificRecordEntity> = {
  pk: commonSchema.pk,
  sk: commonSchema.sk,
  id: {
    type: String,
    required: true,
  },
  key: {
    type: String,
    required: true,
  },
  targetId: {
    type: String,
    required: true,
  },
  createdAt: {
    type: String,
    required: true,
  },
  updatedAt: {
    type: String,
    required: true,
  },
  companyId: {
    type: String,
    required: true,
  },
  domain: {
    type: String,
    required: true,
  },
}

const aoSpecificRecordSchema = new dynamoose.Schema(schemaDefinition, {
  saveUnknown: false,
})

export const AOSpecificRecordDBModel = buildModel<IAOSpecificRecordEntity>(
  aoSpecificRecordSchema,
  commonModelOptions,
)
