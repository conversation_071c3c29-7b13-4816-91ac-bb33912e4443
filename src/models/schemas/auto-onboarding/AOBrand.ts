import { dynamoose } from '../../../common/db/dynamoose/dynamoose'
import { commonModelOptions, commonSchema, TSchemaDefinition } from '../../common/dynamoose'
import { buildModel } from '../../common/utils'
import { TAOBrandContract } from '../../contracts/auto-onboarding/Brand/BrandContract'

export interface IAOBrandKeys extends TAOBrandContract {
  uniqueRecord: undefined
}

interface IAOBrandEntity extends Omit<TAOBrandContract, 'createdAt' | 'updatedAt'> {
  pk: string
  sk: string
  createdAt: string
  updatedAt: string
}

const schemaDefinition: TSchemaDefinition<IAOBrandEntity> = {
  pk: commonSchema.pk,
  sk: commonSchema.sk,
  id: {
    type: String,
    required: true,
  },
  key: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  sourceMerchantId: {
    type: String,
    required: true,
  },
  website: {
    type: String,
    required: false,
  },
  phoneNumber: {
    type: String,
    required: false,
  },
  email: {
    type: String,
    required: false,
  },
  address: {
    type: String,
    required: false,
  },
  createdAt: {
    type: String,
    required: true,
  },
  updatedAt: {
    type: String,
    required: true,
  },
  companyId: {
    type: String,
    required: true,
  },
  domain: {
    type: String,
    required: true,
  },
  specificRecordsIds: {
    type: Array,
    required: false,
    schema: [
      {
        type: String,
        required: false,
      },
    ],
  },
}

const aoBrandSchema = new dynamoose.Schema(schemaDefinition, {
  saveUnknown: false,
})

export const AOBrandDBModel = buildModel<IAOBrandEntity>(aoBrandSchema, commonModelOptions)
