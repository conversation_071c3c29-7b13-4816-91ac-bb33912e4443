import { dynamoose } from '../../../common/db/dynamoose/dynamoose'
import { commonModelOptions, commonSchema, TSchemaDefinition } from '../../common/dynamoose'
import { buildModel } from '../../common/utils'
import { TAOVendorContract } from '../../contracts/auto-onboarding/Vendor/VendorContract'

export interface IAOVendorKeys extends TAOVendorContract {
  uniqueRecord: undefined
}

interface IAOVendorEntity extends Omit<TAOVendorContract, 'createdAt' | 'updatedAt'> {
  pk: string
  sk: string
  createdAt: string
  updatedAt: string
}

const schemaDefinition: TSchemaDefinition<IAOVendorEntity> = {
  pk: commonSchema.pk,
  sk: commonSchema.sk,
  id: {
    type: String,
    required: true,
  },
  key: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  website: {
    type: String,
    required: false,
  },
  license: {
    type: String,
    required: false,
  },
  licenseExpiration: {
    type: String,
    required: false,
  },
  contractFirstName: {
    type: String,
    required: false,
  },
  contractLastName: {
    type: String,
    required: false,
  },
  phone: {
    type: String,
    required: false,
  },
  fax: {
    type: String,
    required: false,
  },
  email: {
    type: String,
    required: false,
  },
  addressStreet: {
    type: String,
    required: false,
  },
  addressCity: {
    type: String,
    required: false,
  },
  addressState: {
    type: String,
    required: false,
  },
  addressZip: {
    type: String,
    required: false,
  },
  description: {
    type: String,
    required: false,
  },
  createdAt: {
    type: String,
    required: true,
  },
  updatedAt: {
    type: String,
    required: true,
  },
  companyId: {
    type: String,
    required: true,
  },
  domain: {
    type: String,
    required: true,
  },
  specificRecordsIds: {
    type: Array,
    required: false,
    schema: [
      {
        type: String,
        required: false,
      },
    ],
  },
}

const aoVendorSchema = new dynamoose.Schema(schemaDefinition, {
  saveUnknown: false,
})

export const AOVendorDBModel = buildModel<IAOVendorEntity>(aoVendorSchema, commonModelOptions)
