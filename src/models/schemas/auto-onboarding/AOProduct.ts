import { dynamoose } from '../../../common/db/dynamoose/dynamoose'
import { commonModelOptions, commonSchema, TSchemaDefinition } from '../../common/dynamoose'
import { buildModel } from '../../common/utils'
import { TAOProductContract } from '../../contracts/auto-onboarding/Product/ProductContract'

export interface IAOProductKeys extends TAOProductContract {
  uniqueRecord: undefined
  batch: string
}

interface IAOProductEntity extends Omit<TAOProductContract, 'createdAt' | 'updatedAt'> {
  pk: string
  sk: string
  createdAt: string
  updatedAt: string
}

const schemaDefinition: TSchemaDefinition<IAOProductEntity> = {
  pk: commonSchema.pk,
  sk: commonSchema.sk,
  id: {
    type: String,
    required: true,
  },
  key: {
    type: String,
    required: true,
  },
  companyId: {
    type: String,
    required: true,
  },
  shopId: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  isActive: {
    type: Boolean,
    required: true,
  },
  isWeedmapsEnabled: {
    type: Boolean,
    required: true,
  },
  saleType: {
    type: String,
    required: false,
  },
  weightPerUnit: {
    type: String,
    required: false,
  },
  weight: {
    type: Number,
    required: false,
  },
  retailPrice: {
    type: Number,
    required: false,
  },
  wholesaleCost: {
    type: Number,
    required: false,
  },
  isAvailableOnline: {
    type: Boolean,
    required: true,
  },
  brandName: {
    type: String,
    required: false,
  },
  categoryName: {
    type: String,
    required: false,
  },
  vendorName: {
    type: String,
    required: false,
  },
  secondaryVendorNames: {
    type: Array,
    required: false,
    schema: [
      {
        type: String,
        required: false,
      },
    ],
  },
  description: {
    type: String,
    required: false,
  },
  flowerType: {
    type: String,
    required: false,
  },
  gramType: {
    type: String,
    required: false,
  },
  imageUrls: {
    type: Array,
    required: false,
    schema: [
      {
        type: String,
        required: false,
      },
    ],
  },
  createdAt: {
    type: String,
    required: true,
  },
  updatedAt: {
    type: String,
    required: true,
  },
  domain: {
    type: String,
    required: true,
  },
  specificRecordsIds: {
    type: Array,
    required: false,
    schema: [
      {
        type: String,
        required: false,
      },
    ],
  },
  tags: {
    type: Array,
    required: false,
    schema: [
      {
        type: String,
        required: false,
      },
    ],
  },
  sku: {
    type: String,
    required: false,
  },
  overrides: {
    type: Array,
    required: true,
    schema: [
      {
        type: Object,
        schema: {
          shopId: {
            type: String,
            required: true,
          },
          sku: {
            type: String,
            required: false,
          },
          isActive: {
            type: Boolean,
            required: false,
          },
          retailPrice: {
            type: Number,
            required: false,
          },
          wholesaleCost: {
            type: Number,
            required: false,
          },
          tags: {
            type: Array,
            required: false,
            schema: [
              {
                type: String,
                required: false,
              },
            ],
          },
        },
      },
    ],
  },
}

const aoProductSchema = new dynamoose.Schema(schemaDefinition, {
  saveUnknown: false,
})

export const AOProductDBModel = buildModel<IAOProductEntity>(aoProductSchema, commonModelOptions)
