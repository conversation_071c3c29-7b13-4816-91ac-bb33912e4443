import { dynamoose } from '../../common/db/dynamoose/dynamoose'
import {
  commonDynamooseSchemaSettings,
  commonModelOptions,
  commonSchema,
} from '../common/dynamoose'
import { ProductType } from '../common/enums'
import { IBaseEntity } from '../common/interfaces'
import { buildModel } from '../common/utils'
import { IProductRetailerPriceAttributes } from './ProductPrice'

export interface IProductRetailerOverrides {
  retailerId: string
  sku: string
  liThreshold: number
  isActive: boolean
}

export interface IProductAttributes {
  name: string
  description: string
  flowerType: string
  tags: string[]
  categoryId: string
  brandId: string
  isAvailableOnline: boolean
  type: ProductType
  images: string[]
  cannabisWeight: number
  cannabisWeightUnit: string
  vendorIds: string[]
  liThreshold: number
  sku: string
  isActive: boolean
  retailerOverrides: IProductRetailerOverrides[]
}

export interface IProductSearchContract extends IProductAttributes {
  unitPrice: number
  retailerPrices: IProductRetailerPriceAttributes[]
}

export interface IProductEntity extends IProductAttributes, IBaseEntity {}

export const productSchema = new dynamoose.Schema(
  {
    pk: commonSchema.pk,
    sk: commonSchema.sk,
    id: commonSchema.id,
    tenantId: commonSchema.tenantId,
    pos: commonSchema.pos,
    recordType: commonSchema.recordType,
    deletedAt: commonSchema.deletedAt,
    name: {
      type: String,
    },
    description: {
      type: String,
    },
    flowerType: {
      type: String,
    },
    tags: {
      type: Array,
      schema: [String],
    },
    categoryId: {
      type: String,
    },
    brandId: {
      type: String,
    },
    isAvailableOnline: {
      type: Boolean,
    },
    type: {
      type: String,
      enum: Object.values(ProductType),
    },
    images: {
      type: Array,
      schema: [String],
    },
    cannabisWeight: {
      type: Number,
    },
    cannabisWeightUnit: {
      type: String,
    },
    vendorIds: {
      type: Array,
      schema: [String],
    },
    isActive: {
      type: Boolean
    },
    liThreshold: {
      type: Number,
    },
    sku: {
      type: String,
    },
    retailerOverrides: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            retailerId: String,
            liThreshold: Number,
            sku: String,
            isActive: Boolean,
          }
        }
      ]
    }
  },
  {
    ...commonDynamooseSchemaSettings,
  },
)

export const Product = buildModel<IProductEntity>(productSchema, commonModelOptions)
