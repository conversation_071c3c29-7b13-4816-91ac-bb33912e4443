import * as dynamoose from 'dynamoose'

export interface IPotency {
  thc?: number
  cbd?: number
  cbn?: number
  cbda?: number
  thca?: number
  min_thc?: number
  max_thc?: number
  min_cbd?: number
  max_cbd?: number
  units?: string
}

export const potencySchema = new dynamoose.Schema({
  thc: {
    type: Number,
  },
  cbd: {
    type: Number,
  },
  cbn: {
    type: Number,
  },
  cbda: {
    type: Number,
  },
  thca: {
    type: Number,
  },
  min_thc: {
    type: Number,
  },
  max_thc: {
    type: Number,
  },
  min_cbd: {
    type: Number,
  },
  max_cbd: {
    type: Number,
  },
  units: {
    type: String,
  },
})
