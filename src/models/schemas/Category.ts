import { dynamoose } from '../../common/db/dynamoose/dynamoose'
import {
  commonDynamooseSchemaSettings,
  commonModelOptions,
  commonSchema,
} from '../common/dynamoose'
import { IBaseEntity } from '../common/interfaces'
import { buildModel } from '../common/utils'

export enum EMeasurementType {
  UNTIS = 'Units',
  GRAMS = 'Grams',
}

export interface IChildCategoryEntity extends IBaseEntity {
  name: string
  parentCategoryId: string
  isActive: boolean
  measurementType: EMeasurementType
  cannabisTypeId: string
  cannabisTypeName: string
  exitLabel: string | null
  image: string
}

export interface ICategoryEntity extends IBaseEntity {
  name: string
  parentCategoryId: string | null
  isActive: boolean
  measurementType: EMeasurementType
  cannabisTypeId: string
  cannabisTypeName: string
  exitLabel: string | null
  image: string
  orderIndex: number
}

export interface IChildCategoryContract {
  id: string
  name: string
  parentCategoryId: string
  isActive: boolean
  measurementType: EMeasurementType
  cannabisTypeId: string
  cannabisTypeName: string
  exitLabel: string | null
  image: string
}

export interface ICategoryContract extends Omit<IChildCategoryContract, 'parentCategoryId'> {

  orderIndex: number
  parentCategoryId: string | null
  childCategories: IChildCategoryContract[]
}

export const categorySchema = new dynamoose.Schema(
  {
    pk: commonSchema.pk,
    sk: commonSchema.sk,
    id: commonSchema.id,
    tenantId: commonSchema.tenantId,
    pos: commonSchema.pos,
    deletedAt: commonSchema.deletedAt,
    name: {
      type: String,
      required: true,
    },
    parentCategoryId: {
      type: String,
    },
    isActive: {
      type: Boolean
    },
    measurementType: {
      type: String,
      required: true,
    },
    cannabisTypeId: {
      type: String,
      required: true,
    },
    cannabisTypeName: {
      type: String,
      required: true,
    },
    exitLabel: {
      type: String,
    },
    image: {
      type: String,
    },
    orderIndex: {
      type: Number,
    },
  },
  {
    ...commonDynamooseSchemaSettings,
  },
)

export const Category = buildModel<ICategoryEntity>(categorySchema, commonModelOptions)
export const ChildCategory = buildModel<IChildCategoryEntity>(categorySchema, commonModelOptions)