import { SchemaDefinition, TimestampObject } from 'dynamoose/dist/Schema'
import { ModelTableOptions } from 'dynamoose/dist/Model'
import { Stage } from '../../common/middy/enums'

const timestamps: TimestampObject = {
  createdAt: {
    createdAt: {
      type: {
        value: String,
        settings: {
          storage: 'iso',
        },
      },
    },
  },
  updatedAt: {
    updatedAt: {
      type: {
        value: String,
        settings: {
          storage: 'iso',
        },
      },
      default: new Date().toISOString(),
    },
  },
}

export const commonDynamooseSchemaSettings = {
  timestamps,
  saveUnknown: ['sourceRecord.**'], // https://dynamoosejs.com/guide/Schema
}

export const commonModelOptions: ModelTableOptions = {
  create: [Stage.LOCAL, Stage.TEST].includes(`${process.env.STAGE}` as Stage),
  waitForActive: { enabled: false },
}

export const commonSchema: SchemaDefinition = {
  pk: {
    type: String,
    hashKey: true,
    required: true,
  },
  sk: {
    type: String,
    rangeKey: true,
    required: true,
  },
  id: {
    type: String,
  },
  tenantId: {
    type: String,
  },
  pos: {
    type: String,
  },
  recordType: {
    type: String,
  },
  deletedAt: {
    type: String,
  },
}

export type TSchemaDefinition<Entity> = {
  [Key in keyof Entity]: Key extends keyof SchemaDefinition ? SchemaDefinition[Key] : never
}
