import { Item } from 'dynamoose/dist/Item'
import { BlazeEventDetailType } from './enums'
export type TDynamoDBEntity<T> = T & Item

export interface IPrimarykeys {
  pk: string
  sk: string
}

export interface IBaseEntity extends IPrimarykeys {
  id: string
  tenantId: string
  pos: string
  recordType: string
  deletedAt: string
  createdAt: string
  updatedAt: string
}

/**
 * gsi-id-recordStatusDate GSI returns
 * - pk
 * - sk
 * - id
 * - recordStatusDate
 */
export interface ILogIdGSIEntity extends IPrimarykeys {
  id: string
  recordStatusDate: string
}

export type TLogByIdGSI<T, U> = TDynamoDBEntity<T extends true ? ILogIdGSIEntity : U>[]

export interface IComputedAttrs {
  tenantId: string
  retailerId: string
  recordStatusDate: string
  createdAt: string
  updatedAt: string
}

export interface IEventBridgeEvent<T> {
  version: string
  id: string
  'detail-type': BlazeEventDetailType | string
  source: string
  account: string
  time: string
  region: string
  resources: string[]
  detail: T
}
