// ISO 3166 country codes
export enum CountryCode {
  US = 'US',
  CA = 'CA',
}

export enum Timezone {
  PACIFIC = 'America/Los_Angeles',
  MOUNTAIN = 'America/Denver',
  CENTRAL = 'America/Chicago',
  EASTERN = 'America/New_York',
}

export enum EntityType {
  PRODUCT = 'product',
}

export enum ProductType {
  STANDALONE = 'STANDALONE',
  VARIANT = 'VARIANT',
  PARENT = 'PARENT',
}

export enum RecordType {
  PRODUCT = 'P',
  CATEGORY = 'C',
  LOCATION = 'L',
  MERCHANT = 'M',
  POS = 'POS',
  TENANT = 'T',
  PRODUCT_PRICE = 'PP',
}

export enum InfoType {
  INFO = 'INFO',
  SALE_PRICE = 'SALE_PRICE',
  PRICE = 'PRICE',
}

export enum ProductWeightType {
  HALF_GRAM = 'HALF_GRAM',
  FULL_GRAM = 'FULL_GRAM',
  EIGHTH = 'EIGHTH',
  CUSTOM_GRAMS = 'CUSTOM_GRAMS',
  EACH = 'EACH',
}

export enum ContentType {
  JSON = 'application/json',
}

export enum BlazeEventDetailType {
  SALE_COMPLETED = 'sale.completed',
  SALE_UPDATED = 'sale.updated',
  MERCHANT_UPDATED = 'merchant.updated',
}

export enum OpensearchIndex {
  PRODUCT = 'product',
}

export enum EDomain {
  CATEGORY = 'categories',
  VENDOR = 'vendors',
  BRAND = 'brands',
  PRODUCT = 'products',
}

export enum EPOSProvider {
  US = 'blu',
  CA = 'blc',
  ECOM = 'ble',
}
