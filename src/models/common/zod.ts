import { z, ZodIssueCode } from 'zod'
import { RegexValidators } from './regex'

export const MongoObjectId = z.string().length(24).regex(RegexValidators.HEX)

export const jsonPreprocessor = (value: any, ctx: z.RefinementCtx) => {
  if (typeof value === 'string') {
    try {
      return JSON.parse(value)
    } catch (e) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: (e as Error).message,
      })
    }
  }

  return value
}
