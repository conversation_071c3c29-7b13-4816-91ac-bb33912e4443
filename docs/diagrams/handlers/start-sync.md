```mermaid
sequenceDiagram
    autonumber

    %% ─────────── Actors ───────────
    actor Client    as Client App
    participant APIGW    as API Gateway
    participant startSync as λ start-sync-fn
    participant Secrets   as AWS Secrets Manager
    participant Blaze<PERSON>I  as Blaze Canada API
    participant SF        as Step Functions
    %% (ResponseWriter is implicit in the reply)

    %% ─────────── Flow ─────────────
    Client  ->> APIGW: POST /companies/{companyId}/sync [force?]
    APIGW   ->> startSync: Invoke Lambda <br/>w/ event payload
    startSync ->> startSync: parseStartSyncEnv()<br/>parseStartSyncEvent()

    alt **query param** force ≠ true
        startSync ->> Secrets: GetSecretValue(BLAZE_CANADA_API_KEY)
        Secrets  -->> startSync: apiKey
        startSync ->> BlazeAPI: GET /companies/{companyId}/categories
        BlazeAPI -->> startSync: category[]
        alt existingCategories.length > 0
            startSync -->> APIGW: 400 Bad Request<br/>“Categories already exist…”
            note over APIGW,Client: <PERSON><PERSON> exits early
        end
    end

    %% ---------- Happy Path ----------
    startSync ->> SF: StartExecution(stateMachineArn, { companyId })
    SF       -->> startSync: { executionArn }
    startSync -->> APIGW: 200 OK<br/>{ executionArn }
    APIGW    -->> Client: Forward HTTP response
```
