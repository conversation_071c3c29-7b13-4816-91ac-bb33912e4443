```mermaid
sequenceDiagram
    autonumber

    %% ─────────────────────────────  Bootstrap  ──────────────────────────────
    participant SF       as Step Functions State Machine
    Note over SF: execution = “companyId + domain”

    %% ─────────────────────────────  Phase 1  ────────────────────────────────
    participant ingestFile as λ *-ingest-task
    participant S3         as S3 SourceFilesBucket
    participant DDB        as DynamoDB AORecordsTable

    SF ->> ingestFile: Invoke categories‑ingest‑task
    ingestFile ->> ingestFile: parseStartSyncEnv()<br/>parseStartSyncEvent()
    ingestFile ->> S3: GetObject(csvKey)
    S3 -->> ingestFile: CSV text
    ingestFile ->> ingestFile: parseCsv()<br/>adapter.parse()<br/>unifyMatchingRecords()
    loop each unified records batch
        ingestFile ->> DDB: BatchPut unified records batch
        DDB -->> ingestFile: OK
    end
    ingestFile -->> SF: {companyId, domain}
```
