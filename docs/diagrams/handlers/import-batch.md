```mermaid
sequenceDiagram
    autonumber

    %% ─────────────────────────────  Bootstrap  ──────────────────────────────
    participant SF          as Step Functions State Machine
    participant importTask  as λ *-import-task
    participant DD<PERSON>         as DynamoDB AORecordsTable
    participant S3          as Blaze Canada S3 Bucket
    participant Blaze       as Blaze Canada API
    participant Secrets     as AWS Secrets Manager

    Note over SF: execution = “companyId + domain"

    %% ────────────────────────────  import-batch  ────────────────────────────

    SF ->> importTask: Invoke <domain>‑import‑task
    importTask ->> importTask: parseStartSyncEnv()<br/>parseStartSyncEvent()
    importTask ->>+ Secrets: GetSecretValue(BLAZE_CANADA_API_KEY)
    Secrets  -->>- importTask: apiKey
    importTask ->>+ DDB: Get unified records
    DDB -->>- importTask: Records list
    alt is a bulk endpoint
        importTask ->> importTask: buildBaseCSV()
        importTask ->>+ S3: PutObjectCommand()
        S3 -->>- importTask: PutObjectCommandOutput
        importTask ->> Blaze: POST /<domain>/bulk
        importTask ->> importTask: buildOverridesCSV()
        importTask ->>+ S3: PutObjectCommand()
        S3 -->>- importTask: PutObjectCommandOutput
        importTask ->> Blaze: POST /<domain>/details-overrides
    else is a record-specific endpoint
        loop each unified record
            importTask ->> Blaze: POST /<domain>
            Blaze -->> importTask: New ID
        end
    end

    importTask -->> SF: {companyId, domain}
```
