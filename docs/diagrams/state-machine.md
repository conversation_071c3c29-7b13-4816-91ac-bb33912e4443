```mermaid
flowchart TB
  subgraph phase1["Phase 1"]
    direction TB
    phase1Parallel["phase-1-parallel-jobs"]
    ingestCategories["categories-ingest-task"]
    ingestVendors["vendors-ingest-task"]
    ingestBrands["brands-ingest-task"]
    importCategories["categories-import-task"]
    importVendors["vendors-import-task"]
    importBrands["brands-import-task"]
  end

  subgraph phase2["Phase 2"]
    direction TB
    phase2Parallel["phase-2-parallel-jobs"]
    ingestProducts["products-ingest-task"]
    importProducts["products-import-task"]
  end

  subgraph sm["State Machine"]
    direction TB
    startSM(("Start"))
    endSM(("End"))
    phase1
    phase2
  end

  ingestCategories --> importCategories
  ingestVendors --> importVendors
  ingestBrands --> importBrands
  phase1Parallel --> ingestCategories & ingestVendors & ingestBrands
  phase2Parallel --> ingestProducts
  ingestProducts --> importProducts
  phase1 -- _If all branches succeed_ --> phase2
  startSM --> phase1
  phase2 --> endSM
  apiGW["API Gateway"] -- POST _/sync_ ---> syncFn["λ start-sync-fn"]
  syncFn --> sm
  apiGW -- GET _/status_ ---> getStatusFn["λ get-status-fn"]
  getStatusFn --> sm
```
