```mermaid
classDiagram
    direction LR

    class CategoryExportRow {
        +id: string
        +shopId: string
        +name: string
        +isActive: boolean
        +unitType: ValidUnitTypes
        +imageUrl?: string
        +isCannabis: boolean
        +cannabisType?: ValidCannabisTypes
        +complianceCategoryId?: string
        +lowInventoryThreshold?: number
    }

    class AOCategoryContract {
        .. Unified & deduped per company ..
        +id: string
        +name: string
        +isActive: boolean
        +unitType: ValidUnitTypes
        +imageUrl?: string
        +cannabisType?: ValidCannabisTypes
        +complianceCategoryId?: string
        +lowInventoryThreshold?: number
        +
        +key: string
        +overrides: AOCategoryOverride[]
        +specificRecordsIds: string[]
        +companyId: string
        +domain: EDomain
        +createdAt: Dayjs
        +updatedAt: Dayjs
    }

    class AOCategoryOverride {
        +shopId: string
        +lowInventoryThreshold?: number
    }

    class CreateBlazeCanadaCategory {
        +name: string
        +measurementType?: MeasurementType
        +categoryType?: CategoryType
        +cannabisType?: string
        +complianceCategoryId?: number
        +lowInventoryThreshold?: number
        +imageUrl?: string
        +isActive?: boolean
        +categoryLocationOverrides?: CategoryLocationOverride[]
        +leaflyCategory?: LeaflyCategory
        +parentCategoryId?: string
        +colorHex?: string
    }

    class CategoryLocationOverride {
        +locationId: number
        +categoryId: string
        +lowInventoryThreshold?: number
    }

    class AOSpecificRecordContract {
        +id: string              <<shop‑row PK>>
        +targetId: string        <<Blaze category ID>>
        +key: string             <<dedupe key>>
        +companyId: string
        +domain: EDomain
        +createdAt: Dayjs
        +updatedAt: Dayjs
    }

    %% ─────────────────────── Relationships ───────────────────────

    CategoryExportRow "*" --> "1" AOCategoryContract : unifyMatchingRecords()
    AOCategoryContract --> CreateBlazeCanadaCategory : toBlazeDTO()
    CreateBlazeCanadaCategory "1" ..> "1" AOSpecificRecordContract : createsMapping()
    AOSpecificRecordContract "1" --> "1" CategoryExportRow
    AOCategoryContract "1" --> "1..n" AOSpecificRecordContract
    AOCategoryContract "1" o-- "1..n" AOCategoryOverride
    CreateBlazeCanadaCategory "0..*" o-- "*" CategoryLocationOverride

```
