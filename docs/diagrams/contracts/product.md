- [ ] add masterId ?? sku ?? category + brand + name matching
- [ ] check for what made the duplicates (sku?) = different products with same SKU
- [ ] replace brand/category/vendor ids with names

# Diagram

```mermaid
classDiagram
    direction LR

    class ProductExportRow {
        +id: string
        +companyId: string
        +shopId: string
        +name: string
        +masterId?: string
        +active: boolean
        +brandId?: string
        +weightPerUnit?: number
        +customGramType?: string
        +customWeight?: number
        +description?: string
        +enableWeedMap: boolean
        +flowerType?: string
        +imageUrl1?: string
        +imageUrl2?: string
        +imageUrl3?: string
        +masterCategoryId?: string
        +productSaleType?: string
        +retailPrice?: number
        +SKU?: string
        +tags?: string
        +vendorId?: string
        +secondaryVendor1?: string
        +secondaryVendor2?: string
        +secondaryVendor3?: string
        +secondaryVendor4?: string
        +secondaryVendor5?: string
        +secondaryVendor6?: string
        +secondaryVendor7?: string
        +secondaryVendor8?: string
        +secondaryVendor9?: string
        +secondaryVendor10?: string
    }

    class AOProductContract {
        +id: string
        +companyId: string
        +shopId: string
        +name: string
        +isActive: boolean
        +brandId?: string
        +weightPerUnit?: number
        +customGramType?: string
        +customWeight?: number
        +description?: string
        +flowerType?: string
        +imageUrls?: string[]
        +masterCategoryId?: string
        +productSaleType?: string
        +retailPrice?: number
        +sku?: string
        +tags?: string[]
        +vendorId?: string
        +secondaryVendorIds?: string[]
        +
        +key: string
        +overrides: AOCategoryOverride[]
        +specificRecordsIds: string[]
        +companyId: string
        +domain: EDomain
        +createdAt: Dayjs
        +updatedAt: Dayjs
    }

    class AOProductOverride {
        +shopId: string
        +retailPrice?: number
        +isActive?: boolean
        +barcode?: string
    }

    class BulkCreateBlazeCanadaProductRow {
        +id: string
        +barcode?: string
        +name: string
        +category: string
        +supplierName: string | null
        +vendorNames: string[]
        +imageUrls: string[] | null
        +tags: string[]
        +purchasePrice?: number
        +price?: number
        +description: string | null
        +isActive: boolean
        +cannabisWeight: number | null
        +unit: Unit
        +strainType: ProductModels.EProductStrainType | null
    }

    class BulkCreateBlazeCanadaProductOverrideRow {
        +locationId: number
        +barcode?: string
        +isActive?: boolean
        +retailPrice?: number
        +tags?: string[]
    }

    %% ─────────────────────── Relationships ───────────────────────

    ProductExportRow "*" --> "1" AOProductContract : unifyMatchingRecords()
    AOProductContract --> BulkCreateBlazeCanadaProductRow : toBlazeDTO()
    AOProductOverride --> BulkCreateBlazeCanadaProductOverrideRow : toBlazeDTO()
    AOProductContract "1" o-- "1..n" AOProductOverride
```
