import { createWorkflow } from '@getgreenline/github-actions-wac'
import { gaUtils } from '@getgreenline/infra-utils'
import {
  apigwRefreshWorkflowCallJobs,
  reusableWorkflowCallJobs,
  testJobs,
  yarnCacheJobs,
} from './presets/workflowJobs'
import { Constants } from './presets/constants'
import { setNpmToken, yarnCache } from '@getgreenline/infra-utils/build/ga-utils'

const { checkout, setupNode, configureAwsCredentials, multiline } = gaUtils

/**
 * Workflows
 *
 * The triggers workflow is used to trigger the deploy workflow for the
 * respective environment.
 */

export const triggers = createWorkflow({
  name: 'Run Test and Trigger Environment Deploy Workflow',
  on: {
    push: {
      branches: ['**'],
    },
    // so that it can be triggered manually from the Actions tab
    workflow_dispatch: {},
  },
  jobs: {
    ...testJobs,
    ...reusableWorkflowCallJobs,
    ...apigwRefreshWorkflowCallJobs,
  },
})

export const deploy = createWorkflow({
  name: 'Build App and Deploy Workflow',
  on: {
    workflow_call: {
      inputs: {
        stage: { required: true, type: 'string' },
        aws_account_number_name: { required: true, type: 'string' },
        aws_region_name: { required: true, type: 'string' },
      },
      secrets: {
        SLACK_BOT_USER_OAUTH_ACCESS_TOKEN: { required: true },
        NPM_TOKEN: { required: true },
        SLACK_DEV_CHANNEL: { required: true },
        SLACK_DEV_DEPLOYMENTS_CHANNEL: { required: true },
      },
    },
  },
  /**
   * These permissions are needed to interact with GitHub's OIDC Token endpoint.
   * Reference: https://github.com/aws-actions/configure-aws-credentials
   */
  permissions: {
    'id-token': 'write',
    contents: 'read',
  },
  jobs: {
    DeployApp: {
      'runs-on': 'ubuntu-latest',
      environment: '${{ inputs.stage }}',
      if: "${{ contains(github.ref, 'refs/heads/main') || contains(github.ref, 'refs/heads/staging') || contains(github.ref, 'refs/heads/sandbox') }}",
      'timeout-minutes': 20,
      steps: [
        checkout({ uses: 'actions/checkout@v3' }),
        setupNode({
          uses: 'actions/setup-node@v4',
          with: {
            'node-version': Constants.NodeVersion,
          },
        }),
        setNpmToken(),
        ...yarnCacheJobs,
        configureAwsCredentials({
          uses: 'aws-actions/configure-aws-credentials@master',
          with: {
            'role-to-assume': 'arn:aws:iam::${{ inputs.aws_account_number_name }}:role/GitHub-OCID',
            'aws-region': '${{ inputs.aws_region_name }}',
          },
        }),
        {
          name: 'Build app',
          run: 'yarn build',
        },
        {
          name: 'CDK bootstrap',
          if: "${{ contains(github.event.head_commit.message, '#bootstrap') }}",
          'working-directory': './infra',
          run: 'yarn bootstrap',
          env: {
            STAGE: '${{ inputs.stage }}',
            DEPLOYED_BY: 'github.actions',
            DEPLOYMENT_ENV: 'ci',
          },
        },
        {
          name: 'Generate a template file',
          'working-directory': './infra',
          run: 'yarn synth:app',
          env: {
            STAGE: '${{ inputs.stage }}',
            DEPLOYED_BY: 'github.actions',
            DEPLOYMENT_ENV: 'ci',
          },
        },
        {
          name: 'Deploy with AWS CDK',
          'working-directory': './infra',
          run: 'yarn deploy:app',
          env: {
            STAGE: '${{ inputs.stage }}',
            DEPLOYED_BY: 'github.actions',
            DEPLOYMENT_ENV: 'ci',
          },
        },
      ],
    },
  },
})
