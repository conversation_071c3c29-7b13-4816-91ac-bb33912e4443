# This file was automatically generated by github-actions-wac.
# DO NOT MODIFY IT BY HAND. Instead, modify the source *.wac.ts file(s)
# and run "github-actions-wac build" (or "ghawac build") to regenerate this file.
# For more information, run "github-actions-wac --help".
name: Build App and Deploy Workflow
'on':
  workflow_call:
    inputs:
      stage:
        required: true
        type: string
      aws_account_number_name:
        required: true
        type: string
      aws_region_name:
        required: true
        type: string
    secrets:
      SLACK_BOT_USER_OAUTH_ACCESS_TOKEN:
        required: true
      NPM_TOKEN:
        required: true
      SLACK_DEV_CHANNEL:
        required: true
      SLACK_DEV_DEPLOYMENTS_CHANNEL:
        required: true
permissions:
  id-token: write
  contents: read
jobs:
  DeployApp:
    runs-on: ubuntu-latest
    environment: ${{ inputs.stage }}
    if: >-
      ${{ contains(github.ref, 'refs/heads/main') || contains(github.ref,
      'refs/heads/staging') || contains(github.ref, 'refs/heads/sandbox') }}
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
      - name: Set NPM Token
        run: >-
          echo //registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }} >
          .npmrc
      - name: Set Yarn cache for app
        uses: actions/cache@v3
        id: app-cache-yarn-cache
        with:
          path: yarn.lock
          key: ${{ runner.os }}-yarn-${{ hashFiles('yarn.lock') }}
          restore-keys: ${{ runner.os }}-yarn-
      - name: Cache node_modules for app
        id: app-cache-node-modules
        uses: actions/cache@v3
        with:
          path: node_modules
          key: >-
            ${{ runner.os }}-${{ matrix.node-version }}-nodemodules-${{
            hashFiles('yarn.lock') }}
          restore-keys: ${{ runner.os }}-${{ matrix.node-version }}-nodemodules-
      - name: Install app dependencies
        if: >-
          steps.app-cache-yarn-cache.outputs.cache-hit != 'true' ||
          steps.app-cache-node-modules.outputs.cache-hit != 'true'
        run: yarn --prefer-offline
      - name: Set Yarn cache for infra
        uses: actions/cache@v3
        id: infra-cache-yarn-cache
        with:
          path: infra/yarn.lock
          key: ${{ runner.os }}-yarn-${{ hashFiles('infra/yarn.lock') }}
          restore-keys: ${{ runner.os }}-yarn-
      - name: Cache node_modules for infra
        id: infra-cache-node-modules
        uses: actions/cache@v3
        with:
          path: infra/node_modules
          key: >-
            ${{ runner.os }}-${{ matrix.node-version }}-nodemodules-${{
            hashFiles('infra/yarn.lock') }}
          restore-keys: ${{ runner.os }}-${{ matrix.node-version }}-nodemodules-
      - name: Install infra dependencies
        if: >-
          steps.infra-cache-yarn-cache.outputs.cache-hit != 'true' ||
          steps.infra-cache-node-modules.outputs.cache-hit != 'true'
        run: yarn --prefer-offline
        working-directory: ./infra
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@master
        with:
          role-to-assume: arn:aws:iam::${{ inputs.aws_account_number_name }}:role/GitHub-OCID
          aws-region: ${{ inputs.aws_region_name }}
      - name: Build app
        run: yarn build
      - name: CDK bootstrap
        if: ${{ contains(github.event.head_commit.message, '#bootstrap') }}
        working-directory: ./infra
        run: yarn bootstrap
        env:
          STAGE: ${{ inputs.stage }}
          DEPLOYED_BY: github.actions
          DEPLOYMENT_ENV: ci
      - name: Generate a template file
        working-directory: ./infra
        run: yarn synth:app
        env:
          STAGE: ${{ inputs.stage }}
          DEPLOYED_BY: github.actions
          DEPLOYMENT_ENV: ci
      - name: Deploy with AWS CDK
        working-directory: ./infra
        run: yarn deploy:app
        env:
          STAGE: ${{ inputs.stage }}
          DEPLOYED_BY: github.actions
          DEPLOYMENT_ENV: ci
