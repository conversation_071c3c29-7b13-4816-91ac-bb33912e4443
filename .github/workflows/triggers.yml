# This file was automatically generated by github-actions-wac.
# DO NOT MODIFY IT BY HAND. Instead, modify the source *.wac.ts file(s)
# and run "github-actions-wac build" (or "ghawac build") to regenerate this file.
# For more information, run "github-actions-wac --help".
name: Run Test and Trigger Environment Deploy Workflow
'on':
  push:
    branches:
      - '**'
  workflow_dispatch: {}
jobs:
  RunTests:
    if: >-
      ${{ contains(github.ref, 'refs/heads/main') || contains(github.ref,
      'refs/heads/staging') || contains(github.ref, 'refs/heads/sandbox') ||
      contains(github.event.head_commit.message, '#runtests') }}
    runs-on: ubuntu-latest
    name: Test
    timeout-minutes: 20
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
      - name: Set NPM Token
        run: >-
          echo //registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }} >
          .npmrc
      - name: Set Yarn cache for app
        uses: actions/cache@v3
        id: app-cache-yarn-cache
        with:
          path: yarn.lock
          key: ${{ runner.os }}-yarn-${{ hashFiles('yarn.lock') }}
          restore-keys: ${{ runner.os }}-yarn-
      - name: Cache node_modules for app
        id: app-cache-node-modules
        uses: actions/cache@v3
        with:
          path: node_modules
          key: >-
            ${{ runner.os }}-${{ matrix.node-version }}-nodemodules-${{
            hashFiles('yarn.lock') }}
          restore-keys: ${{ runner.os }}-${{ matrix.node-version }}-nodemodules-
      - name: Install app dependencies
        if: >-
          steps.app-cache-yarn-cache.outputs.cache-hit != 'true' ||
          steps.app-cache-node-modules.outputs.cache-hit != 'true'
        run: yarn --prefer-offline
      - name: Set Yarn cache for infra
        uses: actions/cache@v3
        id: infra-cache-yarn-cache
        with:
          path: infra/yarn.lock
          key: ${{ runner.os }}-yarn-${{ hashFiles('infra/yarn.lock') }}
          restore-keys: ${{ runner.os }}-yarn-
      - name: Cache node_modules for infra
        id: infra-cache-node-modules
        uses: actions/cache@v3
        with:
          path: infra/node_modules
          key: >-
            ${{ runner.os }}-${{ matrix.node-version }}-nodemodules-${{
            hashFiles('infra/yarn.lock') }}
          restore-keys: ${{ runner.os }}-${{ matrix.node-version }}-nodemodules-
      - name: Install infra dependencies
        if: >-
          steps.infra-cache-yarn-cache.outputs.cache-hit != 'true' ||
          steps.infra-cache-node-modules.outputs.cache-hit != 'true'
        run: yarn --prefer-offline
        working-directory: ./infra
      - name: Extract current branch name
        run: echo "BRANCH_NAME=${GITHUB_REF#refs/heads/}" >> $GITHUB_ENV
      - name: Download build artifacts
        uses: actions/download-artifact@v4
      - name: Start DynamoDB
        run: >-
          docker run -d -p 4566:4566 amazon/dynamodb-local -jar
          DynamoDBLocal.jar -port 4566
      - name: Wait for DynamoDB to be set up
        run: . scripts/wait-for-dynamodb.sh
      - name: Run app tests
        run: yarn test:app
      - name: Cancel entire workflow immediately on test failure
        if: ${{ failure() }}
        uses: GetGreenline/cancel-action@72f3be561937e7cefd2f42b6817c0b371b4e9f43
  TestNotifyFailure:
    runs-on: ubuntu-latest
    needs:
      - RunTests
    if: always() && needs.RunTests.result != 'success'
    timeout-minutes: 2
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Extract current branch name
        run: echo "BRANCH_NAME=${GITHUB_REF#refs/heads/}" >> $GITHUB_ENV
      - name: Extract current branch name and other github info
        run: |-
          echo "GITHUB_REPOSITORY=${GITHUB_REPOSITORY}" >> $GITHUB_ENV
          echo "GITHUB_RUN_ID=${GITHUB_RUN_ID}" >> $GITHUB_ENV
      - name: Determine which job failed
        id: failed_job
        uses: actions/github-script@v6
        with:
          script: >-
            const needs = JSON.parse(process.env.NEEDS)

            console.log(needs)

            return Object.keys(needs).reduce((acc, job) => acc === '' &&
            needs[`${job}`].result === 'failure' ? job : acc, '')
          result-encoding: string
        env:
          NEEDS: ${{ toJSON(needs) }}
      - name: Send message to Slack API
        uses: >-
          GetGreenline/github-actions-slack@416029e5d5cac6ccce0b1993e76dc99c9a897954
        id: notify
        with:
          slack-bot-user-oauth-access-token: ${{ secrets.SLACK_BOT_USER_OAUTH_ACCESS_TOKEN }}
          slack-channel: >-
            ${{ env.BRANCH_NAME == 'main' && secrets.SLACK_DEV_CHANNEL ||
            secrets.SLACK_DEV_DEPLOYMENTS_CHANNEL }}
          slack-optional-unfurl_links: false
          slack-text: >-
            ${{ inputs.environment }} Greenline-API Automated Test Failure

            *Merger*: ${{ github.event.sender.login }}

            *Workflow*: https://github.com/${{ env.GITHUB_REPOSITORY
            }}/actions/runs/${{ env.GITHUB_RUN_ID }}
          slack-optional-icon_emoji: ':warning:'
  DeployApp_sandbox:
    name: Trigger sandbox deployment
    with:
      stage: sandbox
      aws_account_number_name: '************'
      aws_region_name: us-east-1
    needs: RunTests
    if: >-
      contains(github.ref, 'refs/heads/sandbox') && needs.RunTests.result ==
      'success'
    uses: ./.github/workflows/deploy.yml
    secrets:
      SLACK_BOT_USER_OAUTH_ACCESS_TOKEN: ${{ secrets.SLACK_BOT_USER_OAUTH_ACCESS_TOKEN }}
      NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      SLACK_DEV_CHANNEL: ${{ secrets.SLACK_DEV_CHANNEL }}
      SLACK_DEV_DEPLOYMENTS_CHANNEL: ${{ secrets.SLACK_DEV_DEPLOYMENTS_CHANNEL }}
  DeployApp_staging:
    name: Trigger staging deployment
    with:
      stage: staging
      aws_account_number_name: '************'
      aws_region_name: us-east-1
    needs: RunTests
    if: >-
      contains(github.ref, 'refs/heads/staging') && needs.RunTests.result ==
      'success'
    uses: ./.github/workflows/deploy.yml
    secrets:
      SLACK_BOT_USER_OAUTH_ACCESS_TOKEN: ${{ secrets.SLACK_BOT_USER_OAUTH_ACCESS_TOKEN }}
      NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      SLACK_DEV_CHANNEL: ${{ secrets.SLACK_DEV_CHANNEL }}
      SLACK_DEV_DEPLOYMENTS_CHANNEL: ${{ secrets.SLACK_DEV_DEPLOYMENTS_CHANNEL }}
  DeployApp_prod:
    name: Trigger prod deployment
    with:
      stage: main
      aws_account_number_name: '************'
      aws_region_name: us-east-1
    needs: RunTests
    if: >-
      contains(github.ref, 'refs/heads/main') && needs.RunTests.result ==
      'success'
    uses: ./.github/workflows/deploy.yml
    secrets:
      SLACK_BOT_USER_OAUTH_ACCESS_TOKEN: ${{ secrets.SLACK_BOT_USER_OAUTH_ACCESS_TOKEN }}
      NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      SLACK_DEV_CHANNEL: ${{ secrets.SLACK_DEV_CHANNEL }}
      SLACK_DEV_DEPLOYMENTS_CHANNEL: ${{ secrets.SLACK_DEV_DEPLOYMENTS_CHANNEL }}
  RefreshApiGateway_sandbox:
    name: Refresh API Gateway stage
    needs: DeployApp_sandbox
    if: always() && needs.DeployApp_sandbox.result == 'success'
    with:
      environment: sandbox
    uses: GetGreenline/blaze-infra-core/.github/workflows/api-deploy.yml@main
    secrets:
      SLACK_BOT_USER_OAUTH_ACCESS_TOKEN: ${{ secrets.SLACK_BOT_USER_OAUTH_ACCESS_TOKEN }}
  RefreshApiGateway_staging:
    name: Refresh API Gateway stage
    needs: DeployApp_staging
    if: always() && needs.DeployApp_staging.result == 'success'
    with:
      environment: staging
    uses: GetGreenline/blaze-infra-core/.github/workflows/api-deploy.yml@main
    secrets:
      SLACK_BOT_USER_OAUTH_ACCESS_TOKEN: ${{ secrets.SLACK_BOT_USER_OAUTH_ACCESS_TOKEN }}
  RefreshApiGateway_prod:
    name: Refresh API Gateway stage
    needs: DeployApp_prod
    if: always() && needs.DeployApp_prod.result == 'success'
    with:
      environment: prod
    uses: GetGreenline/blaze-infra-core/.github/workflows/api-deploy.yml@main
    secrets:
      SLACK_BOT_USER_OAUTH_ACCESS_TOKEN: ${{ secrets.SLACK_BOT_USER_OAUTH_ACCESS_TOKEN }}
