# reference: https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/workbench.html
name: product-service
services:
  localstack:
    container_name: localstack
    image: localstack/localstack
    ports:
      - "127.0.0.1:4566:4566"
      - "127.0.0.1:4510-4559:4510-4559" # ext services port range
      - "127.0.0.1:8000:8000"
    environment:
      - DEBUG=1
      - PERSISTENCE=1
      - DOCKER_HOST=unix:///var/run/docker.sock
      - AWS_DEFAULT_REGION=us-east-1
      - ALLOW_NONSTANDARD_REGIONS=1
    volumes:
      - "${LOCALSTACK_VOLUME_DIR:-/var/lib/localstack}:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
