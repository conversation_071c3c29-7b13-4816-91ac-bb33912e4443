# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temp folder
*/tmp

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript v1 declaration files
typings/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

*/.jest/cache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.*
*.env
!.env.test

# CDK asset staging directory
.cdk.staging
cdk.out
cdk.context.json
template.yaml

# next.js build output
.next
dist

# Local setup
local-setup/tmp

# Editor directories and files
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store
tags
.tags
.vscode

*.zip

# REPL History
.repl_history

test-files/**