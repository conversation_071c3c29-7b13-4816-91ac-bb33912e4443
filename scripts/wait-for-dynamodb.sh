#!/bin/bash
# reference: https://github.com/formkiq/formkiq-core/blob/master/wait-for-dynamodb.sh

export AWS_ACCESS_KEY_ID=none
export AWS_SECRET_ACCESS_KEY=none
export AWS_SESSION_TOKEN=none
export AWS_DEFAULT_REGION=us-east-1

until aws --region us-east-1 --endpoint-url=http://localhost:4566 dynamodb list-tables; do
  >&2 echo "DynamoDB is unavailable - sleeping"
  sleep 1
done

echo "DynamoDB is available"