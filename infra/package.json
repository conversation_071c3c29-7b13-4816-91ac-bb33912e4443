{"name": "product-service-infra", "version": "0.1.0", "bin": {"product-service": "bin/index.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest --config jest.config.js --runInBand", "cdk": "cdk", "clean-cdk": "rm -rf cdk.out cdk.context.json", "bootstrap": "cdk bootstrap", "synth:app": "yarn clean-cdk && cdk synth --validation --app \"ts-node --prefer-ts-exts bin/index.ts\" > template.yaml", "synth:app:local": "export STAGE=local && yarn synth:app", "deploy:app": "cdk deploy --method=direct --app \"npx ts-node --prefer-ts-exts bin/index.ts\" --require-approval never --all"}, "dependencies": {"@getgreenline/infra-utils": "^2.0.0", "aws-cdk-lib": "^2.145.0", "aws-sdk": "^2.1640.0", "constructs": "^10.3.0", "dotenv": "^16.4.5", "js-yaml": "^4.1.0", "source-map-support": "^0.5.21"}, "devDependencies": {"@types/js-yaml": "^4.0.9", "@types/node": "^20.14.2", "aws-cdk": "^2.145.0", "ts-node": "^10.9.2", "typescript": "^5.4.5"}}