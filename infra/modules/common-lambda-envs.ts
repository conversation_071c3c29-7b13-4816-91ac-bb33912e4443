import { FunctionOptions } from 'aws-cdk-lib/aws-lambda'
import { config } from '../config'
import { GetSecret } from '../lib/constructs/sm'
import { Environment } from '../lib/envs'
import { AppStage } from '../lib/enums'

export const getCommonLambdaEnvs = (sm: GetSecret): FunctionOptions['environment'] => {
  if (config.validatedEnvs.STAGE === AppStage.LOCAL) {
    return {
      ...config.validatedEnvs,
    }
  }

  // TODO: `unsafeUnwrap` is the easiest way to get secrets but it's not the best way. Please update this in the future
  let envs: any = Object.keys(Environment).reduce((prev, key) => {
    const envValue = sm.secret.secretValueFromJson(key).unsafeUnwrap()

    return {
      ...prev,
      [key]: envValue,
    }
  }, {})

  envs = {
    ...envs,
    STAGE: config.stage,
  }

  return envs
}
