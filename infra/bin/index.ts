#!/usr/bin/env node
import 'source-map-support/register'
import { App, StackProps } from 'aws-cdk-lib'
import { config } from '../config'
import { stageValue } from '../lib/utils'
import { AutoOnboardingStack } from '../stacks/auto-onboarding-stack'
import { CompanyProductsStack } from '../stacks/company-products-stack'
import { ProductServiceStack } from '../stacks/product-service-stack'

export const createStacks = (app: App) => {
  const commonStackProps: StackProps = {
    env: config.stack.env,
    terminationProtection: stageValue.bool(
      {
        production: true,
        staging: false,
      },
      false,
    ),
    tags: {
      Project: config.projectName,
      DeployedBy: config.deployedBy,
      GithubRepo: config.githubRepo,
      'map-migrated': 'migF81OV6IAO3',
    },
  }

  const productServiceStack = new ProductServiceStack(app, `${config.projectName}-stack`, {
    ...commonStackProps,
    description: `Resources for Blaze ${config.stage} Product Service`,
  })

  const autoOnboardingStack = new AutoOnboardingStack(
    app,
    `${config.projectName}-auto-onboarding-stack`,
    {
      ...commonStackProps,
      description: `Resources for Blaze ${config.stage} Auto Onboarding`,
    },
  )

  const companyProductsStack = new CompanyProductsStack(
    app,
    `${config.projectName}-company-products-stack`,
    {
      ...commonStackProps,
      description: `Resources for Blaze ${config.stage} Company Products`,
    },
  )

  return [productServiceStack, autoOnboardingStack, companyProductsStack]
}

/**
 * We use an async function to initialize the CDK App so that we can make asynchronous calls to
 * the AWS SDK to get any information we need before deploying the CDK instead of using the CDK provided
 * context resolution to get that information
 */
export const createApp = () => {
  const app = new App()

  const stacks = createStacks(app)

  return { app, stacks }
}

if (process.env.NODE_ENV !== 'test') createApp()
