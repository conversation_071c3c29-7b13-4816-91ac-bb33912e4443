import { stageValue } from '../lib/utils'

const testingLedgergreenBaseUrl = 'https://sandbox-api.ledgergreen.com/v1'

const testingSwifterAuthUrl = 'https://auth.preprod.swifterhq.com'
const testingSwifterAPIUrl = 'https://api.preprod.swifterhq.com'

const testingAdyenTerminalPaymentUrl = 'https://terminal-api-test.adyen.com'
const testingAdyenOnlinePaymentUrl = 'https://checkout-test.adyen.com/v71'
const testingAdyenManagementUrl = 'https://management-test.adyen.com/v3'

const testingZentactApiUrl = 'https://test.api.zentact.com'

const testingTFITransactionUrl = 'https://tfiblaze.rest.uat.iconncloud.net'
const testingTFINonTransactionUrl = 'https://tfiblaze.api.uat.iconncloud.net'

const testingBlazeEcomBaseUrl = 'https://api-qa.tymber.io'
const testingBlazeCanadaBaseUrl = 'https://sandbox-core-api.getgreenline.co'
const testingBlazeUSABaseUrl = 'https://api.staging.blaze.me'

export const paymentServiceUrls = {
  PAYMENT_SERVICE_BASE_URL: stageValue.str(
    {
      production: 'https://gateway.blaze.me/payment-service',
      staging: 'https://gateway.staging.blaze.me/payment-service',
      sandbox: 'https://gateway.fb-sandbox.blaze.me/payment-service',
    },
    'https://gateway.fb-sandbox.blaze.me/payment-service',
  ),
}

export const externalUrls = {
  LEDGER_GREEN_BASE_URL: stageValue.str(
    {
      production: 'https://api.ledgergreen.com/v1',
      staging: testingLedgergreenBaseUrl,
      sandbox: testingLedgergreenBaseUrl,
    },
    testingLedgergreenBaseUrl,
  ),
  SWIFTER_AUTH_URL: stageValue.str(
    {
      production: 'https://auth.swifterhq.com',
      staging: testingSwifterAuthUrl,
      sandbox: testingSwifterAuthUrl,
    },
    testingSwifterAuthUrl,
  ),
  SWIFTER_API_URL: stageValue.str(
    {
      production: 'https://api.swifterhq.com',
      staging: testingSwifterAPIUrl,
      sandbox: testingSwifterAPIUrl,
    },
    testingSwifterAPIUrl,
  ),
  ADYEN_TERMINAL_PAYMENT_URL: stageValue.str(
    {
      production: 'https://terminal-api-live-us.adyen.com',
      staging: testingAdyenTerminalPaymentUrl,
      sandbox: testingAdyenTerminalPaymentUrl,
    },
    testingAdyenTerminalPaymentUrl,
  ),
  ADYEN_ONLINE_PAYMENT_URL: stageValue.str(
    {
      production:
        'https://e7500061857cc74c-BlazeSolutionsGreenlinePO-checkout-live.adyenpayments.com/checkout/v71',
      staging: testingAdyenOnlinePaymentUrl,
      sandbox: testingAdyenOnlinePaymentUrl,
    },
    testingAdyenOnlinePaymentUrl,
  ),
  ADYEN_MANAGEMENT_URL: stageValue.str(
    {
      production: 'https://management-live.adyen.com/v3',
      staging: testingAdyenManagementUrl,
      sandbox: testingAdyenManagementUrl,
    },
    testingAdyenManagementUrl,
  ),
  ZENTACT_API_URL: stageValue.str(
    {
      production: 'https://api.zentact.com',
      staging: testingZentactApiUrl,
      sandbox: testingZentactApiUrl,
    },
    testingZentactApiUrl,
  ),
  TFI_TRANSACTION_URL: stageValue.str(
    {
      production: 'https://tfiblaze.rest.iconncloud.net',
      staging: testingTFITransactionUrl,
      sandbox: testingTFITransactionUrl,
    },
    testingTFITransactionUrl,
  ),
  TFI_NON_TRANSACTION_URL: stageValue.str(
    {
      production: 'https://tfiblaze.api.iconncloud.net',
      staging: testingTFINonTransactionUrl,
      sandbox: testingTFINonTransactionUrl,
    },
    testingTFINonTransactionUrl,
  ),
  BLAZE_ECOMM_BASE_URL: stageValue.str(
    {
      production: 'https://api.tymber.io',
      staging: testingBlazeEcomBaseUrl,
      sandbox: testingBlazeEcomBaseUrl,
    },
    testingBlazeEcomBaseUrl,
  ),
  BLAZE_CANADA_BASE_URL: stageValue.str(
    {
      production: 'https://api.getgreenline.co',
      staging: 'https://core-staging-api.getgreenline.co',
      sandbox: testingBlazeCanadaBaseUrl,
    },
    testingBlazeCanadaBaseUrl,
  ),
  BLAZE_USA_BASE_URL: stageValue.str(
    {
      production: 'https://api.blaze.me',
      staging: testingBlazeUSABaseUrl,
      sandbox: testingBlazeUSABaseUrl,
    },
    testingBlazeUSABaseUrl,
  ),
}

export const getRootApiIdentifiers = (
  type: 'internal' | 'external' = 'internal',
): {
  restApiId: string
  rootResourceId: string
  requestAuthorizerId: string
} => {
  const defaultSandboxIdSet = {
    restApiId: 'uull0jol7c',
    rootResourceId: '2fstzlrns9',
    requestAuthorizerId: type == 'internal' ? 't54lde' : 'txrga2',
  }

  const idSet = stageValue.other(
    {
      sandbox: defaultSandboxIdSet,
      staging: {
        restApiId: 'hhzvqjyf53',
        rootResourceId: 'i8p70sicxi',
        requestAuthorizerId: 'chot67',
      },
      production: {
        restApiId: 'm46yz77nh3',
        rootResourceId: 'rpybv08mvh',
        requestAuthorizerId: 'nwu0am',
      },
    },
    defaultSandboxIdSet,
  )

  return idSet || defaultSandboxIdSet
}
