import {
  Stack,
  StackProps,
  aws_dynamodb as dynamodb,
  aws_lambda as lambda,
  aws_events as events,
} from 'aws-cdk-lib'
import { Construct } from 'constructs'
import { DynamoDB } from '../lib/constructs/dynamodb'
import { config } from '../config'
import { stageValue } from '../lib/utils'
import { GroupedLambdaFunctions } from '../lib/constructs/lambda'
import { getRootApiIdentifiers } from '../constants/api'
import {
  ExistingAPIGatewayRestApi,
  getRequestAuthorizer,
  NestedApiResources,
} from '../lib/constructs/apigw'
import { HttpMethods } from '../lib/enums'
import { ServerlessOpenSearch } from '../lib/constructs/opensearchserverless'

export class ProductServiceStack extends Stack {
  constructor(scope: Construct, id: string, stackProps: StackProps) {
    super(scope, id, stackProps)

    const blazePlatformEventBus = events.EventBus.fromEventBusName(
      this,
      `${config.projectName}-platform-eventbus`,
      config.aws.event.eventBusName,
    )

    const serverlessOpenSearch = new ServerlessOpenSearch(this, {
      collectionName: 'ps',
    })

    const productDB = new DynamoDB(this, {
      prefix: config.projectName,
      tableName: `${config.projectName}-table`,
      deletionProtection: stageValue.bool(
        {
          production: true,
          staging: false,
        },
        false,
      ),
      partitionKey: {
        name: 'pk',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: 'sk',
        type: dynamodb.AttributeType.STRING,
      },
      stream: dynamodb.StreamViewType.NEW_IMAGE,
    })

    const commonLambdaProps = {
      prefix: config.projectName,
      timeoutSecs: 30,
      memoryMB: 1024,
      tracing: lambda.Tracing.ACTIVE,
      environment: {
        EVENT_BUS_NAME: config.aws.event.eventBusName,
        TABLE_NAME: productDB.table.tableName,
        STAGE: config.stage,
        AOSS_ENDPOINT: serverlessOpenSearch.searchDomain,
      },
    }

    const productLambdaGroup = new GroupedLambdaFunctions(this, {
      type: 'Product',
      functionProps: {
        GetProduct: {
          ...commonLambdaProps,
          sourceCodePath: '../dist/handlers/product/get-product',
          functionName: 'get-product',
          handler: 'index.getProduct',
        },
        UpdateProduct: {
          ...commonLambdaProps,
          sourceCodePath: '../dist/handlers/product/update-product',
          functionName: 'update-product',
          handler: 'index.updateProduct',
        },
        CreateProduct: {
          ...commonLambdaProps,
          sourceCodePath: '../dist/handlers/product/create-product',
          functionName: 'create-product',
          handler: 'index.createProduct',
        },
        DeleteProduct: {
          ...commonLambdaProps,
          sourceCodePath: '../dist/handlers/product/delete-product',
          functionName: 'delete-product',
          handler: 'index.deleteProduct',
        },
        SearchProduct: {
          ...commonLambdaProps,
          sourceCodePath: '../dist/handlers/product/search-product',
          functionName: 'search-product',
          handler: 'index.searchProduct',
        },
      },
    })

    const productPriceLambdaGroup = new GroupedLambdaFunctions(this, {
      type: 'Product Prices',
      functionProps: {
        GetProductPrice: {
          ...commonLambdaProps,
          sourceCodePath: '../dist/handlers/product-price/get-product-price',
          functionName: 'get-product-price',
          handler: 'index.getProductPrice',
        },
        UpdateProductPrice: {
          ...commonLambdaProps,
          sourceCodePath: '../dist/handlers/product-price/update-product-price',
          functionName: 'update-product-price',
          handler: 'index.updateProductPrice',
        },
        CreateProductPrice: {
          ...commonLambdaProps,
          sourceCodePath: '../dist/handlers/product-price/create-product-price',
          functionName: 'create-product-price',
          handler: 'index.createProductPrice',
        },
      },
    })

    const categoryLambdaGroup = new GroupedLambdaFunctions(this, {
      type: 'Category',
      functionProps: {
        GetCategory: {
          ...commonLambdaProps,
          sourceCodePath: '../dist/handlers/category/get-category',
          functionName: 'get-category',
          handler: 'index.getCategory',
        },
        CreateCategory: {
          ...commonLambdaProps,
          sourceCodePath: '../dist/handlers/category/create-category',
          functionName: 'create-category',
          handler: 'index.createCategory',
        },
        UpdateCategory: {
          ...commonLambdaProps,
          sourceCodePath: '../dist/handlers/category/update-category',
          functionName: 'update-category',
          handler: 'index.updateCategory',
        },
        ListCategories: {
          ...commonLambdaProps,
          sourceCodePath: '../dist/handlers/category/list-categories',
          functionName: 'list-categories',
          handler: 'index.listCategories',
        },
        DeleteCategory: {
          ...commonLambdaProps,
          sourceCodePath: '../dist/handlers/category/delete-category',
          functionName: 'delete-category',
          handler: 'index.deleteCategory',
        },
      },
    })

    const rootApiIdentifiers = getRootApiIdentifiers()
    const requestAuthorizer = getRequestAuthorizer(rootApiIdentifiers.requestAuthorizerId)

    const api = new ExistingAPIGatewayRestApi(this, {
      prefix: `${config.projectName}-${config.validatedEnvs.STAGE}`,
      serviceName: 'product-service',
      resources: ['api'],
    })
    api.setDeployment(this)

    const v1 = api.baseResource.addResource('v1')

    const v1BaseMerchantResource = v1.addResource('merchants').addResource('{merchantId}')
    const v1BaseProductResource = v1BaseMerchantResource.addResource('products')
    const v1BaseCategoryResource = v1BaseMerchantResource.addResource('categories')
    const v1BaseProductIdResource = v1BaseProductResource.addResource('{productId}')
    const v1BaseProductPriceResource = v1BaseProductIdResource.addResource('prices')

    new NestedApiResources(this, {
      baseResource: v1BaseProductResource,
      requestAuthorizer,
      routes: [
        {
          resourcePath: ['search'],
          integrations: [
            {
              method: HttpMethods.POST,
              handler: productLambdaGroup.functionMap.SearchProduct,
              apigwMethodOptions: {
                operationName: `Search Product`,
              },
            },
          ],
        },
      ],
    })

    new NestedApiResources(this, {
      baseResource: v1BaseProductResource,
      requestAuthorizer,
      routes: [
        {
          resourcePath: [],
          integrations: [
            {
              method: HttpMethods.POST,
              handler: productLambdaGroup.functionMap.CreateProduct,
              apigwMethodOptions: {
                operationName: `Create Product`,
              },
            },
          ],
        },
      ],
    })

    new NestedApiResources(this, {
      baseResource: v1BaseProductIdResource,
      requestAuthorizer,
      routes: [
        {
          resourcePath: [],
          integrations: [
            {
              method: HttpMethods.GET,
              handler: productLambdaGroup.functionMap.GetProduct,
              apigwMethodOptions: {
                operationName: `Get Product`,
              },
            },
            {
              method: HttpMethods.PUT,
              handler: productLambdaGroup.functionMap.UpdateProduct,
              apigwMethodOptions: {
                operationName: `Update Product`,
              },
            },
            {
              method: HttpMethods.DELETE,
              handler: productLambdaGroup.functionMap.DeleteProduct,
              apigwMethodOptions: {
                operationName: `Delete Product`,
              },
            },
          ],
        },
      ],
    })

    new NestedApiResources(this, {
      baseResource: v1BaseProductPriceResource,
      requestAuthorizer,
      routes: [
        {
          resourcePath: [],
          integrations: [
            {
              method: HttpMethods.POST,
              handler: productPriceLambdaGroup.functionMap.CreateProductPrice,
              apigwMethodOptions: {
                operationName: `Create Product Price`,
              },
            },
            {
              method: HttpMethods.GET,
              handler: productPriceLambdaGroup.functionMap.GetProductPrice,
              apigwMethodOptions: {
                operationName: `Get Product Prices`,
              },
            },
            {
              method: HttpMethods.PUT,
              handler: productPriceLambdaGroup.functionMap.UpdateProductPrice,
              apigwMethodOptions: {
                operationName: `Update Product Prices`,
              },
            },
          ],
        },
      ],
    })

    new NestedApiResources(this, {
      baseResource: v1BaseCategoryResource,
      requestAuthorizer,
      routes: [
        {
          resourcePath: [],
          integrations: [
            {
              method: HttpMethods.POST,
              handler: categoryLambdaGroup.functionMap.CreateCategory,
              apigwMethodOptions: {
                operationName: `Create Category`,
              },
            },
            {
              method: HttpMethods.GET,
              handler: categoryLambdaGroup.functionMap.ListCategories,
              apigwMethodOptions: {
                operationName: `List Categories`,
              },
            },
          ],
        },
        {
          resourcePath: ['{categoryId}'],
          integrations: [
            {
              method: HttpMethods.GET,
              handler: categoryLambdaGroup.functionMap.GetCategory,
              apigwMethodOptions: {
                operationName: `Get Category`,
              },
            },
            {
              method: HttpMethods.PUT,
              handler: categoryLambdaGroup.functionMap.UpdateCategory,
              apigwMethodOptions: {
                operationName: `Update Category`,
              },
            },
            {
              method: HttpMethods.DELETE,
              handler: categoryLambdaGroup.functionMap.DeleteCategory,
              apigwMethodOptions: {
                operationName: `Delete Category`,
              },
            },
          ],
        },
      ],
    })

    productDB.table.grantReadData(productLambdaGroup.functionMap.GetProduct.lambdaFnAlias)
    productDB.table.grantReadWriteData(productLambdaGroup.functionMap.CreateProduct.lambdaFnAlias)
    productDB.table.grantReadWriteData(productLambdaGroup.functionMap.UpdateProduct.lambdaFnAlias)
    productDB.table.grantReadWriteData(productLambdaGroup.functionMap.DeleteProduct.lambdaFnAlias)
    productDB.table.grantReadData(categoryLambdaGroup.functionMap.GetCategory.lambdaFnAlias)
    productDB.table.grantReadWriteData(categoryLambdaGroup.functionMap.CreateCategory.lambdaFnAlias)
    productDB.table.grantReadWriteData(categoryLambdaGroup.functionMap.UpdateCategory.lambdaFnAlias)
    productDB.table.grantReadData(categoryLambdaGroup.functionMap.ListCategories.lambdaFnAlias)
    productDB.table.grantReadWriteData(categoryLambdaGroup.functionMap.DeleteCategory.lambdaFnAlias)
    productDB.table.grantReadData(productPriceLambdaGroup.functionMap.GetProductPrice.lambdaFnAlias)
    productDB.table.grantReadWriteData(
      productPriceLambdaGroup.functionMap.CreateProductPrice.lambdaFnAlias,
    )
    productDB.table.grantReadWriteData(
      productPriceLambdaGroup.functionMap.UpdateProductPrice.lambdaFnAlias,
    )

    blazePlatformEventBus.grantPutEventsTo(
      productLambdaGroup.functionMap.CreateProduct.lambdaFnAlias,
    )
    blazePlatformEventBus.grantPutEventsTo(
      productLambdaGroup.functionMap.UpdateProduct.lambdaFnAlias,
    )
    blazePlatformEventBus.grantPutEventsTo(
      productLambdaGroup.functionMap.DeleteProduct.lambdaFnAlias,
    )
    blazePlatformEventBus.grantPutEventsTo(
      categoryLambdaGroup.functionMap.CreateCategory.lambdaFnAlias,
    )
    blazePlatformEventBus.grantPutEventsTo(
      categoryLambdaGroup.functionMap.UpdateCategory.lambdaFnAlias,
    )
    blazePlatformEventBus.grantPutEventsTo(
      categoryLambdaGroup.functionMap.DeleteCategory.lambdaFnAlias,
    )
    blazePlatformEventBus.grantPutEventsTo(
      productPriceLambdaGroup.functionMap.CreateProductPrice.lambdaFnAlias,
    )
    blazePlatformEventBus.grantPutEventsTo(
      productPriceLambdaGroup.functionMap.UpdateProductPrice.lambdaFnAlias,
    )

    serverlessOpenSearch.collection.setupDynamodbIngestion(productDB.table.tableArn)
    serverlessOpenSearch.collection.grantSearchDocumentsAccess([
      productLambdaGroup.functionMap.SearchProduct.lambdaFn,
    ])
  }
}
