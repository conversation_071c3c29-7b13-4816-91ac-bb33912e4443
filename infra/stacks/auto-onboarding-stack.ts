import { Stack, StackProps } from 'aws-cdk-lib'
import { Construct } from 'constructs'
import { GroupedLambdaFunctions } from '../lib/constructs/lambda'
import { config } from '../config'
import { S3Bucket } from '../lib/constructs/s3'
import { SFStateMachine, SFStateMachineState } from '../lib/constructs/stepfunctions/statemachine'
import { GetSecret } from '../lib/constructs/sm'
import { EDomain, HttpMethods } from '../lib/enums'
import { SFParallelJobs } from '../lib/constructs/stepfunctions/applied/parallel-jobs'
import { DDBAttributeType, DynamoDB } from '../lib/constructs/dynamodb'
import { stageValue } from '../lib/utils'
import {
  ExistingAPIGatewayRestApi,
  getRequestAuthorizer,
  NestedApiResources,
} from '../lib/constructs/apigw'
import { getRootApiIdentifiers } from '../constants/api'
import { GetStringListSSMParameter, GetSystemParameter } from '../lib/constructs/ssm'
import { SFLambdaTask } from '../lib/constructs/stepfunctions/task'

export class AutoOnboardingStack extends Stack {
  constructor(scope: Construct, id: string, stackProps: StackProps) {
    super(scope, id, stackProps)

    const { secret: blazeCanadaAPIKeySecret } = new GetSecret(this, {
      secretName: config.aws.sm.blazeCanadaAPIKeySecretName,
      prefix: config.projectName,
    })

    const autoOnboardingSourceBucket = new S3Bucket(this, {
      name: 'auto-onboarding-source',
      writeonlyExternalPrincipalArns: config.aws.iam.blazeUSECSTaskRoleARNs,
    })

    const autoOnboardingTargetBucket = new S3Bucket(this, {
      name: 'auto-onboarding-target',
      readonlyExternalPrincipalArns: config.aws.iam.blazeCanadaECSTaskRoleARNs,
    })

    const { value: platformEventBusName } = new GetSystemParameter(this, {
      prefix: config.projectName,
      path: config.aws.ssm.platformEventBusName,
    })

    const { value: glProductRuleArn } = new GetSystemParameter(this, {
      prefix: config.projectName,
      path: config.aws.ssm.productRuleArn,
    })

    const { value: usProductRuleArn } = new GetSystemParameter(this, {
      prefix: config.projectName,
      path: config.aws.ssm.usProductRuleArn,
    })

    const enableEventParameter = new GetStringListSSMParameter(this, {
      prefix: config.projectName,
      parameterName: '/platform-product-events/companyIds',
    })

    const { table: autoOnboardingTable } = new DynamoDB(this, {
      prefix: config.projectName,
      tableName: `${config.projectName}-auto-onboarding-table`,
      deletionProtection: stageValue.bool({ production: true }, false),
      partitionKey: {
        name: 'pk',
        type: DDBAttributeType.STRING,
      },
      sortKey: {
        name: 'sk',
        type: DDBAttributeType.STRING,
      },
    })

    // TODO: Add ARN to the parameter for each ENV
    const enableEventsLambda = new GroupedLambdaFunctions(this, {
      type: 'Enable Events',
      environment: {
        STAGE: config.stage,
      },
      functionProps: {
        EnableEvents: {
          sourceCodePath: '../dist/handlers/auto-onboarding/enable-events',
          functionName: 'enable-events',
          handler: 'index.enableEvents',
          memoryMB: 256,
          prefix: config.projectName,
          timeoutSecs: 120,
          environment: {
            PLATFORM_PRODUCT_EVENT_BUS_NAME: platformEventBusName,
            GL_PRODUCT_EVENT_RULE_NAME: config.aws.event.glProductRuleName,
            US_PRODUCT_EVENT_RULE_NAME: config.aws.event.usProductRuleName,
          },
          rolePermissions: [
            {
              actions: [
                'events:PutRule',
                'events:EnableRule',
                'events:PutTargets',
                'events:RemoveTargets',
                'events:DescribeRule',
              ],
              resources: [glProductRuleArn, usProductRuleArn],
            },
          ],
        },
      },
    })

    const autoOnboardingLambdas = new GroupedLambdaFunctions(this, {
      type: 'Auto Onboarding Processing',
      environment: {
        STAGE: config.stage,
        TABLE_NAME: autoOnboardingTable.tableName,
      },
      functionProps: {
        IngestFile: {
          sourceCodePath: '../dist/handlers/auto-onboarding/ingest-file',
          functionName: 'ingest-file',
          handler: 'index.ingestFile',
          memoryMB: 1024,
          prefix: config.projectName,
          timeoutSecs: 900,
          environment: {
            SOURCE_FILES_BUCKET_NAME: autoOnboardingSourceBucket.bucket.bucketName,
          },
        },
        ImportBatch: {
          sourceCodePath: '../dist/handlers/auto-onboarding/import-batch',
          functionName: 'import-batch',
          handler: 'index.importBatch',
          memoryMB: 1024,
          prefix: config.projectName,
          timeoutSecs: 900,
          environment: {
            BLAZE_CANADA_API_KEY_SECRET_NAME: blazeCanadaAPIKeySecret.secretName,
            BLAZE_CANADA_BULK_CSV_BUCKET_NAME: autoOnboardingTargetBucket.bucket.bucketName,
          },
        },
      },
    })

    // STATE MACHINE
    const { task: enableEventsTask } = new SFLambdaTask(scope, {
      name: `enable-events-task`,
      lambdaFn: enableEventsLambda.functionMap.EnableEvents.lambdaFn,
      payload: {
        'companyId.$': '$$.Execution.Input.companyId',
      },
      outputPath: '$.Payload',
    })

    const { jobsGroup: phase1Jobs } = new SFParallelJobs(this, {
      phase: 1,
      ingestFn: autoOnboardingLambdas.functionMap.IngestFile.lambdaFn,
      importFn: autoOnboardingLambdas.functionMap.ImportBatch.lambdaFn,
      jobs: [
        {
          domain: EDomain.CATEGORY,
        },
        {
          domain: EDomain.VENDOR,
        },
        {
          domain: EDomain.BRAND,
        },
      ],
    })

    const { jobsGroup: phase2Jobs } = new SFParallelJobs(this, {
      phase: 2,
      ingestFn: autoOnboardingLambdas.functionMap.IngestFile.lambdaFn,
      importFn: autoOnboardingLambdas.functionMap.ImportBatch.lambdaFn,
      jobs: [
        {
          domain: EDomain.PRODUCT,
          iterateImport: true,
        },
      ],
    })

    const { stateMachine: aoStateMachine } = new SFStateMachine(this, {
      name: 'company-auto-onboarding-state-machine',
      definition: enableEventsTask
        .next(phase1Jobs)
        .next(phase2Jobs)
        .next(new SFStateMachineState.Succeed(this, 'Done')),
      timeout: 900,
    })

    // API LAMBDAS

    const autoOnboardingAPILambdas = new GroupedLambdaFunctions(this, {
      type: 'Auto Onboarding API',
      environment: {
        STAGE: config.stage,
        STATE_MACHINE_ARN: aoStateMachine.stateMachineArn,
      },
      functionProps: {
        StartSync: {
          sourceCodePath: '../dist/handlers/auto-onboarding/start-sync',
          functionName: 'start-sync',
          handler: 'index.startSync',
          memoryMB: 128,
          prefix: config.projectName,
          timeoutSecs: 120,
          environment: {
            BLAZE_CANADA_API_KEY_SECRET_NAME: blazeCanadaAPIKeySecret.secretName,
          },
        },
      },
    })

    // API

    const rootApiIdentifiers = getRootApiIdentifiers()
    const requestAuthorizer = getRequestAuthorizer(rootApiIdentifiers.requestAuthorizerId)

    const api = new ExistingAPIGatewayRestApi(this, {
      prefix: config.projectName,
      serviceName: 'auto-onboarding',
      resources: ['api'],
    })
    api.setDeployment(this)

    const v1 = api.baseResource.addResource('v1')
    const v1BaseCompanyResource = v1.addResource('companies').addResource('{companyId}')

    new NestedApiResources(this, {
      baseResource: v1BaseCompanyResource,
      requestAuthorizer,
      routes: [
        {
          resourcePath: ['sync'],
          integrations: [
            {
              method: HttpMethods.POST,
              handler: autoOnboardingAPILambdas.functionMap.StartSync,
              apigwMethodOptions: {
                operationName: `Start Company Sync`,
              },
            },
          ],
        },
      ],
    })

    // PERMISSIONS

    autoOnboardingSourceBucket.bucket.grantRead(
      autoOnboardingLambdas.functionMap.IngestFile.lambdaFn,
    )

    autoOnboardingTargetBucket.bucket.grantWrite(
      autoOnboardingLambdas.functionMap.ImportBatch.lambdaFn,
    )

    blazeCanadaAPIKeySecret.grantRead(autoOnboardingLambdas.functionMap.ImportBatch.lambdaFn)
    blazeCanadaAPIKeySecret.grantRead(autoOnboardingAPILambdas.functionMap.StartSync.lambdaFn)

    autoOnboardingTable.grantWriteData(autoOnboardingLambdas.functionMap.IngestFile.lambdaFn)
    autoOnboardingTable.grantReadWriteData(autoOnboardingLambdas.functionMap.ImportBatch.lambdaFn)

    aoStateMachine.grantStartExecution(autoOnboardingAPILambdas.functionMap.StartSync.lambdaFn)

    enableEventParameter.parameter.grantRead(enableEventsLambda.functionMap.EnableEvents.lambdaFn)
    enableEventParameter.parameter.grantWrite(enableEventsLambda.functionMap.EnableEvents.lambdaFn)
  }
}
