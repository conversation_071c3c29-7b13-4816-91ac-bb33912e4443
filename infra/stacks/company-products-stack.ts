import { aws_events as events, Stack, StackProps, aws_events_targets as targets } from 'aws-cdk-lib'
import { Construct } from 'constructs'
import { config } from '../config'
import { getRootApiIdentifiers } from '../constants/api'
import {
  ExistingAPIGatewayRestApi,
  getRequestAuthorizer,
  NestedApiResources,
} from '../lib/constructs/apigw'
import { GroupedEventRules } from '../lib/constructs/eventbridge/eventbridge.rules'
import { GroupedLambdaFunctions } from '../lib/constructs/lambda'
import { ServerlessOpenSearch } from '../lib/constructs/opensearchserverless'
import { GroupedSQS } from '../lib/constructs/sqs'
import { EventSources, EventTopics, EventTypes, HttpMethods } from '../lib/enums'

export class CompanyProductsStack extends Stack {
  constructor(scope: Construct, id: string, stackProps: StackProps) {
    super(scope, id, stackProps)

    // OPENSEARCH

    const { searchDomain: ossSearchDomain, collection: ossCollection } = new ServerlessOpenSearch(
      this,
      {
        collectionName: 'cps',
      },
    )

    // EVENTBUS

    const blazePlatformEventBus = events.EventBus.fromEventBusName(
      this,
      `${config.projectName}-platform-eventbus`,
      config.aws.event.eventBusName,
    )

    // SQS QUEUES

    const { sqsMap: dlqQueues } = new GroupedSQS(this, {
      sqsProps: {
        productIngestionsDLQ: {
          queueName: `product-ingestions-dlq`,
          visibilityTimeout: 300,
        },
      },
    })

    const { sqsMap: queues } = new GroupedSQS(this, {
      sqsProps: {
        productIngestions: {
          queueName: 'product-ingestions',
          visibilityTimeout: 3600,
          dlq: {
            queue: dlqQueues.productIngestionsDLQ.queue,
            maxReceiveCount: 3,
          },
        },
      },
    })

    // API LAMBDAS

    const companyProductsLambdas = new GroupedLambdaFunctions(this, {
      type: 'Company Products',
      environment: {
        STAGE: config.stage,
        EVENT_BUS_NAME: config.aws.event.eventBusName,
        AOSS_ENDPOINT: ossSearchDomain,
      },
      functionProps: {
        ingestProducts: {
          sourceCodePath: '../dist/handlers/company-products/ingest-products',
          functionName: 'ingest-products',
          handler: 'index.replayHandler',
          memoryMB: 128,
          prefix: config.projectName,
          timeoutSecs: 120,
          eventSources: [
            {
              queueSource: {
                queue: queues.productIngestions.queue,
                props: {
                  batchSize: 10,
                  maxConcurrency: 3,
                  reportBatchItemFailures: true,
                },
              },
            },
          ],
        },
        searchProducts: {
          sourceCodePath: '../dist/handlers/company-products/search-products',
          functionName: 'search-products',
          handler: 'index.handler',
          memoryMB: 128,
          prefix: config.projectName,
          timeoutSecs: 120,
        },
      },
    })

    // API

    const rootApiIdentifiers = getRootApiIdentifiers()
    const requestAuthorizer = getRequestAuthorizer(rootApiIdentifiers.requestAuthorizerId)

    const api = new ExistingAPIGatewayRestApi(this, {
      prefix: config.projectName,
      serviceName: 'company-products',
      resources: ['api'],
    })
    api.setDeployment(this)

    const v1 = api.baseResource.addResource('v1')
    const v1BaseCompanyResource = v1.addResource('companies').addResource('{companyId}')

    new NestedApiResources(this, {
      baseResource: v1BaseCompanyResource,
      requestAuthorizer,
      routes: [
        {
          resourcePath: ['search'],
          integrations: [
            {
              method: HttpMethods.GET,
              handler: companyProductsLambdas.functionMap.searchProducts,
              apigwMethodOptions: {
                operationName: `Search company products in OS`,
              },
            },
          ],
        },
      ],
    })

    // EB RULES

    new GroupedEventRules(this, {
      ruleProps: {
        productEventsIngestion: {
          eventBus: blazePlatformEventBus,
          ruleName: `${config.projectName}-company-product-ingestion-rule`,
          description: `Routes product create/update events`,
          eventPattern: {
            source: [EventSources.GREENLINE_API],
            detailType: [
              `${EventTopics.PRODUCT}.${EventTypes.CREATED}`,
              `${EventTopics.PRODUCT}.${EventTypes.UPDATED}`,
            ],
          },
          targets: [new targets.SqsQueue(queues.productIngestions.queue)],
        },
      },
    })

    // PERMISSIONS

    ossCollection.grantManageIndexesAccess([
      companyProductsLambdas.functionMap.ingestProducts.lambdaFn,
    ])
    ossCollection.grantIndexDocumentsAccess([
      companyProductsLambdas.functionMap.ingestProducts.lambdaFn,
    ])
    ossCollection.grantSearchDocumentsAccess([
      companyProductsLambdas.functionMap.searchProducts.lambdaFn,
    ])

    queues.productIngestions.queue.grantConsumeMessages(
      companyProductsLambdas.functionMap.ingestProducts.lambdaFn,
    )
  }
}
