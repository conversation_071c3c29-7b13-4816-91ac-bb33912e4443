export enum GithubBranch {
  SANDBOX = 'sandbox',
  STAGING = 'staging',
  PRODUCTION = 'main',
}

export enum AppStage {
  SANDBOX = 'sandbox',
  STAGING = 'staging',
  PRODUCTION = 'prod',
  TEST = 'test',
  LOCAL = 'local',
}

export enum AppStageProfiles {
  PRODUCTION = 'greenline-albert',
  STAGING = 'greenline-staging',
  SANDBOX = 'greenline-sandbox',
}

export enum DeploymentEnvironments {
  LOCAL = 'local',
  CI = 'CI',
}

export enum HttpMethods {
  OPTIONS = 'OPTIONS',
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  PATCH = 'PATCH',
  DELETE = 'DELETE',
}

export enum LambdaMetricActions {
  SUCCEEDED = 'succeeded',
  ERRORED = 'errored',
  FAULTED = 'faulted',
  THROTTLED = 'throttled',
}

export enum EventTopics {
  SALE = 'sale',
  MERCHANT = 'merchant',
  PRODUCT = 'product',
}

export enum EventTypes {
  CREATED = 'created',
  UPDATED = 'updated',
  DELETED = 'deleted',
  COMPLETED = 'completed',
}

export enum EventSources {
  PRODUCT_SERVICE = 'co.blaze.product-service',
  GREENLINE_API = 'co.getgreenline.api',
}

export enum EDomain {
  CATEGORY = 'categories',
  VENDOR = 'vendors',
  BRAND = 'brands',
  PRODUCT = 'products',
}
