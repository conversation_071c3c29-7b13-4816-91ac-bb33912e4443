import { REQUIRED_ENV_VARIABLES, ENV_VARIABLES, SetAppStageProfile } from './types'
import { AppStage, AppStageProfiles, DeploymentEnvironments, GithubBranch } from './enums'

export const setAppStageProfile: SetAppStageProfile = {
  prod: AppStageProfiles.PRODUCTION,
  staging: AppStageProfiles.STAGING,
  sandbox: AppStageProfiles.SANDBOX,
}

/** Necessary if deploying from localhost */
export const getDeploymentStage = (githubBranch: string | undefined): AppStage => {
  if (githubBranch === GithubBranch.PRODUCTION) {
    return AppStage.PRODUCTION
  }
  if (githubBranch === GithubBranch.STAGING) {
    return AppStage.STAGING
  }

  if (githubBranch === GithubBranch.SANDBOX) {
    return AppStage.SANDBOX
  }

  if (githubBranch === DeploymentEnvironments.LOCAL) {
    return AppStage.LOCAL
  }

  throw new Error(`Invalid STAGE detected`)
}

// Necessary to remove circular dependency with config file
export const STAGE = getDeploymentStage(process.env.STAGE)
export const ACCOUNT_NUMBER = process.env.CDK_DEFAULT_ACCOUNT

export const validateEnv = (
  requiredEnvs: Array<keyof REQUIRED_ENV_VARIABLES>,
  env: { [key: string]: string | undefined },
): ENV_VARIABLES => {
  requiredEnvs.forEach((requiredEnv: string) => {
    if (!env[requiredEnv]) {
      throw new Error(`${requiredEnv} required`)
    }
  })

  return env as ENV_VARIABLES
}

export const isBoolean = (val: unknown): boolean => 'boolean' === typeof val

export const stageValue = {
  /** return a number value depending on the stage  */
  num(
    {
      production,
      staging,
      sandbox,
    }: {
      production?: number
      staging?: number
      sandbox?: number
    },
    defaultValue: number,
  ): number {
    if (STAGE === AppStage.PRODUCTION && Number.isFinite(production)) return production as number
    if (STAGE === AppStage.STAGING && Number.isFinite(staging)) return staging as number
    if (STAGE === AppStage.SANDBOX && Number.isFinite(sandbox)) return sandbox as number

    return defaultValue
  },
  /** return a boolean value depending on the stage  */
  bool(
    {
      production,
      staging,
      sandbox,
    }: {
      production?: boolean
      staging?: boolean
      sandbox?: boolean
    },
    defaultValue: boolean,
  ): boolean {
    if (STAGE === AppStage.PRODUCTION && isBoolean(production)) return production as boolean
    if (STAGE === AppStage.STAGING && isBoolean(staging)) return staging as boolean
    if (STAGE === AppStage.SANDBOX && isBoolean(sandbox)) return sandbox as boolean

    return defaultValue
  },
  /** return a string value depending on the stage */
  str(
    {
      production,
      staging,
      sandbox,
    }: {
      production?: string
      staging?: string
      sandbox?: string
    },
    defaultValue: string,
  ): string {
    if (STAGE === AppStage.PRODUCTION && production) return production as string
    if (STAGE === AppStage.STAGING && staging) return staging as string
    if (STAGE === AppStage.SANDBOX && sandbox) return sandbox as string

    return defaultValue
  },
  other<T>(
    {
      production,
      staging,
      sandbox,
    }: {
      production?: T
      staging?: T
      sandbox?: T
    },
    defaultValue: T,
  ): T {
    if (STAGE === AppStage.PRODUCTION && production) return production
    if (STAGE === AppStage.STAGING && staging) return staging
    if (STAGE === AppStage.SANDBOX && sandbox) return sandbox

    return defaultValue
  },
}

/**
 * Some AWS resources do not allow names greater than 32 chars.
 * Use this function to set a shorter name if needed.
 */
export const useShortName = (name: string, shortName: string, charLimit = 32): string => {
  return name.length > charLimit ? shortName : name
}

/**
 * Constructs an SSO Role ARN based on the role identifier.
 * @param roleIdentifier - The unique role identifier (the part after `/aws-reserved/sso.amazonaws.com/`).
 * @returns The constructed ARN string.
 */
export const buildSSORoleArn = (roleIdentifier: string): string => {
  return `arn:aws:iam::${ACCOUNT_NUMBER}:role/aws-reserved/sso.amazonaws.com/${roleIdentifier}`
}

export const toPascalCase = (text: string) => {
  const camelCaseText = text.trim().replace(/[-_\s]+(.)?/g, (_, c) => (c ? c.toUpperCase() : ''))
  return camelCaseText.charAt(0).toUpperCase() + camelCaseText.slice(1)
}
