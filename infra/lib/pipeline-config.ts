export function getPipelineConfigStr({
  tableArn,
  bucketName,
  collectionEndpoint,
  networkPolicyName,
  pipelineRoleArn,
}: {
  tableArn: string
  bucketName: string
  collectionEndpoint: string
  networkPolicyName: string
  pipelineRoleArn: string
}) {
  return `version: "2"
dynamodb-pipeline:
  source:
    dynamodb:
      tables:
        - table_arn: "${tableArn}"
          stream:
            start_position: "LATEST"
          export:
            s3_bucket: "${bucketName}"
            s3_region: "us-east-1"
            s3_prefix: "export/"
      aws:
        sts_role_arn: "${pipelineRoleArn}"
        region: "us-east-1"
  processor:
    - delete_entries:
        with_keys: ["pk", "sk"]
  routes:
    - product: '/recordType == "P"'
    - product_prices: '/recordType == "PP"'
  sink:
    - opensearch:
        hosts: [ "${collectionEndpoint}" ]
        index: "product"
        routes: [ "product" ]
        document_id: "\${/id}"
        actions:
          - type: "upsert"
        flush_timeout: -1              
        aws:
          sts_role_arn: "${pipelineRoleArn}"
          region: "us-east-1"
          serverless: true
          serverless_options:
            network_policy_name: "${networkPolicyName}"
        dlq:
          s3:
            bucket: "${bucketName}"
            key_path_prefix: "dlq/product"
            region: "us-east-1"
            sts_role_arn: "${pipelineRoleArn}"
    - opensearch:
        hosts: [ "${collectionEndpoint}" ]
        index: "product"
        routes: [ "product_prices" ]
        exclude_keys: ["companyProductId", "recordType"]
        document_id: "\${/companyProductId}"
        actions:
          - type: "update"
        flush_timeout: -1              
        aws:
          sts_role_arn: "${pipelineRoleArn}"
          region: "us-east-1"
          serverless: true
          serverless_options:
            network_policy_name: "${networkPolicyName}"
        dlq:
          s3:
            bucket: "${bucketName}"
            key_path_prefix: "dlq/product"
            region: "us-east-1"
            sts_role_arn: "${pipelineRoleArn}"`
}
