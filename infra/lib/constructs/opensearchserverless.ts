import * as opensearchserverless from 'aws-cdk-lib/aws-opensearchserverless'
import { Construct } from 'constructs'
import { config, getOpenSearchSSORoleArns } from '../../config'
import {
  Arn,
  aws_iam as iam,
  aws_appsync as appsync,
  RemovalPolicy,
  aws_osis,
  CfnOutput,
} from 'aws-cdk-lib'
import { stageValue } from '../utils'
import { Role, ServicePrincipal } from 'aws-cdk-lib/aws-iam'
import { BlockPublicAccess, Bucket, BucketEncryption } from 'aws-cdk-lib/aws-s3'
import { LogGroup, RetentionDays } from 'aws-cdk-lib/aws-logs'
import { getPipelineConfigStr } from '../pipeline-config'

interface IProps {
  collectionName: string
}

class OpenSearchConstruct extends Construct {
  public readonly searchDomain: string
  public readonly collection: opensearchserverless.CfnCollection
  public readonly netPolicy: opensearchserverless.CfnSecurityPolicy

  constructor(scope: Construct, props: IProps) {
    super(scope, `${config.projectAcronym}-oss-opensearch`)

    const collectionName = `${config.projectAcronym}-${props.collectionName}-col`

    // See https://docs.aws.amazon.com/opensearch-service/latest/developerguide/serverless-manage.html
    const collection = new opensearchserverless.CfnCollection(this, collectionName, {
      name: collectionName,
      type: 'SEARCH',
      standbyReplicas: stageValue.str({ production: 'ENABLED' }, 'DISABLED'),
      description: 'Products Service collection',
    })

    // Encryption policy is needed in order for the collection to be created
    const encPolicyName = `${collectionName}-ec-pol`
    const encPolicyObj = {
      Rules: [{ ResourceType: 'collection', Resource: [`collection/${collectionName}`] }],
      AWSOwnedKey: true,
    }

    const encPolicy = new opensearchserverless.CfnSecurityPolicy(this, encPolicyName, {
      name: encPolicyName,
      policy: JSON.stringify(encPolicyObj),
      type: 'encryption',
    })
    collection.node.addDependency(encPolicy)

    // Network policy is required so that the dashboard can be viewed
    const netPolicyName = `${collectionName}-nw-pol`
    const netPolicyObj = {
      Rules: [
        { ResourceType: 'collection', Resource: [`collection/${collectionName}`] },
        { ResourceType: 'dashboard', Resource: [`collection/${collectionName}`] },
      ],
      AllowFromPublic: true,
    }

    const netPolicy = new opensearchserverless.CfnSecurityPolicy(this, netPolicyName, {
      name: netPolicyName,
      policy: `[${JSON.stringify(netPolicyObj)}]`,
      type: 'network',
    })
    collection.node.addDependency(netPolicy)

    this.searchDomain = collection.attrCollectionEndpoint
    this.collection = collection
    this.netPolicy = netPolicy
  }

  public grantAccess({
    collection,
    resources,
    permissionSlug,
    indexPermissions,
    collectionPermissions,
  }: {
    collection: opensearchserverless.CfnCollection
    resources: iam.IGrantable[]
    permissionSlug: string
    indexPermissions?: string[]
    collectionPermissions?: string[]
  }) {
    for (const resource of resources) {
      const principalName = resource.grantPrincipal.toString()

      // The appSyncDSRoleArn is used only for appsync, because it is not possible to get the DS role ARN from the IGrantable interface
      const appSyncDSRoleArn = (resource as Partial<appsync.HttpDataSource>)?.ds?.serviceRoleArn

      // Roles can be stack-name/.../role-name or stack-name/.../resource-name/ServiceRole
      const roleName = principalName.split('/').pop()
      const roleArn =
        appSyncDSRoleArn ??
        Arn.format({
          service: 'iam',
          resource: 'role',
          resourceName: roleName,
          account: config.stack.env.account,
          region: '',
          partition: 'aws',
        })

      const securityPolicyJSON = {
        Description: 'Data Access to OpenSearch',
        Rules: [
          ...(indexPermissions
            ? [
                {
                  ResourceType: 'index',
                  Resource: [`index/${collection.name}/*`],
                  Permission: indexPermissions,
                },
              ]
            : []),
          ...(collectionPermissions
            ? [
                {
                  ResourceType: 'collection',
                  Resource: [`collection/${collection.name}`],
                  Permission: collectionPermissions,
                },
              ]
            : []),
        ],
        Principal: [roleArn],
      }

      const roleAcronym = roleName
        ?.replace(config.projectAcronym, '')
        .replace(config.projectName, '')
        .replace('role', '')
        .split('-')
        .map(([firstChar]) => firstChar)
        .join('')
        .toLowerCase()

      const collectionAcronym = collection.name.replace('-col', '')

      const dataPolicyName = `${collectionAcronym}-${roleAcronym}-${permissionSlug}-pol`
      new opensearchserverless.CfnAccessPolicy(this, dataPolicyName, {
        name: dataPolicyName,
        policy: `[${JSON.stringify(securityPolicyJSON)}]`,
        type: 'data',
      })

      resource.grantPrincipal.addToPrincipalPolicy(
        new iam.PolicyStatement({
          // This is required for the resource to be able to send requests to the OpenSearch endpoint
          // It does not grant access to the data IN the OS collections
          actions: ['aoss:APIAccessAll', 'aoss:BatchGetCollection'],
          effect: iam.Effect.ALLOW,
          resources: [collection.attrArn],
        }),
      )
    }
  }

  public setupDynamodbIngestion(tableArn: string): void {
    const bucketName = `${config.projectAcronym}-dynamodb-oss-etl`
    const bucket = new Bucket(this, bucketName, {
      bucketName,
      blockPublicAccess: BlockPublicAccess.BLOCK_ALL,
      encryption: BucketEncryption.S3_MANAGED,
      autoDeleteObjects: false,
      removalPolicy: RemovalPolicy.DESTROY,
    })

    const logGroupName = `/aws/vendedlogs/OpenSearchIngestion/dynamodb-osis-pipeline/audit-logs`
    new LogGroup(this, `${config.projectAcronym}-oss-etl-log`, {
      logGroupName,
      removalPolicy: RemovalPolicy.DESTROY,
      retention: RetentionDays.ONE_DAY,
    })

    // Create OpenSearch Ingestion Pipeline Role
    const pipelineRoleName = `${config.projectAcronym}-oss-etl-role`
    const pipelineRole = new Role(this, pipelineRoleName, {
      assumedBy: new ServicePrincipal('osis-pipelines.amazonaws.com'),
    })

    const collectionName = this.collection.name

    // Construct the security policy as a normal object
    const securityPolicyJSON = {
      Rules: [
        {
          ResourceType: 'collection',
          Resource: [`collection/${collectionName}*`],
          Permission: [
            'aoss:CreateCollectionItems',
            'aoss:DescribeCollectionItems',
            'aoss:DeleteCollectionItems',
            'aoss:UpdateCollectionItems',
          ],
        },
        {
          ResourceType: 'index',
          Resource: [`index/${collectionName}*/*`],
          Permission: [
            'aoss:CreateIndex',
            'aoss:DeleteIndex',
            'aoss:UpdateIndex',
            'aoss:DescribeIndex',
            'aoss:ReadDocument',
            'aoss:WriteDocument',
          ],
        },
      ],
      Principal: [pipelineRole.roleArn, ...getOpenSearchSSORoleArns()],
    }

    // Create the OpenSearch data access policy
    const dataAccessPolicy = new opensearchserverless.CfnAccessPolicy(
      this,
      `${config.projectAcronym}-ddb-etl-access`,
      {
        name: `${config.projectAcronym}-ddb-etl-access`,
        type: 'data',
        description: `Data access policy for ${collectionName} collection.`,
        policy: JSON.stringify([securityPolicyJSON]), // Convert the object to a JSON string
      },
    )

    this.collection.node.addDependency(dataAccessPolicy)

    // OpenSearch Ingestion Pipeline Policy
    const ingestionPipelinePolicyName = `${config.projectAcronym}-oss-pip-ingestion`
    const ingestionPipelinePolicy = new iam.Policy(this, ingestionPipelinePolicyName, {
      policyName: ingestionPipelinePolicyName,
      statements: [
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: ['aoss:*'],
          resources: [this.collection.attrArn],
        }),
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            'aoss:CreateSecurityPolicy',
            'aoss:GetSecurityPolicy',
            'aoss:UpdateSecurityPolicy',
          ],
          conditions: {
            StringEquals: {
              'aoss:collection': collectionName,
            },
          },
          resources: ['*'],
        }),
      ],
    })

    // DynamoDB and S3 Access Policy
    const dynamodbIngestionPolicyName = `${config.projectAcronym}-oss-db-ingestion`
    const dynamodbIngestionPolicy = new iam.Policy(this, dynamodbIngestionPolicyName, {
      policyName: dynamodbIngestionPolicyName,
      statements: [
        new iam.PolicyStatement({
          sid: 'allowRunExportJob',
          effect: iam.Effect.ALLOW,
          actions: [
            'dynamodb:DescribeTable',
            'dynamodb:DescribeContinuousBackups',
            'dynamodb:ExportTableToPointInTime',
          ],
          resources: [tableArn],
        }),
        new iam.PolicyStatement({
          sid: 'allowCheckExportjob',
          effect: iam.Effect.ALLOW,
          actions: ['dynamodb:DescribeExport'],
          resources: [`${tableArn}/export/*`],
        }),
        new iam.PolicyStatement({
          sid: 'allowReadFromStream',
          effect: iam.Effect.ALLOW,
          actions: ['dynamodb:DescribeStream', 'dynamodb:GetRecords', 'dynamodb:GetShardIterator'],
          resources: [`${tableArn}/stream/*`],
        }),
        new iam.PolicyStatement({
          sid: 'allowReadAndWriteToS3ForExport',
          effect: iam.Effect.ALLOW,
          actions: ['s3:GetObject', 's3:AbortMultipartUpload', 's3:PutObject', 's3:PutObjectAcl'],
          resources: [`${bucket.bucketArn}/*`],
        }),
      ],
    })

    // Attach policies to the pipeline role
    pipelineRole.attachInlinePolicy(ingestionPipelinePolicy)
    pipelineRole.attachInlinePolicy(dynamodbIngestionPolicy)
    pipelineRole.grantPrincipal.addToPrincipalPolicy(
      new iam.PolicyStatement({
        actions: ['aoss:APIAccessAll', 'aoss:BatchGetCollection'],
        effect: iam.Effect.ALLOW,
        resources: [this.collection.attrArn],
      }),
    )

    const pipelineConfigYAML = getPipelineConfigStr({
      tableArn,
      bucketName,
      collectionEndpoint: this.collection.attrCollectionEndpoint,
      networkPolicyName: this.netPolicy.name,
      pipelineRoleArn: pipelineRole.roleArn,
    })

    new CfnOutput(this, 'BucketName', {
      value: `${bucketName}`,
    })

    new CfnOutput(this, 'TableArn', {
      value: `${tableArn}`,
    })

    new CfnOutput(this, 'networkPolicyName', {
      value: `${this.netPolicy.name}`,
    })

    new CfnOutput(this, 'collectionEndpoint', {
      value: `${this.collection.attrCollectionEndpoint}`,
    })

    new CfnOutput(this, 'PipelineRoleArn', {
      value: `${pipelineRole.roleArn}`,
    })

    const pipelineName = `${config.projectAcronym}-oss-etl-pipeline`
    const pipeline = new aws_osis.CfnPipeline(this, pipelineName, {
      pipelineName,
      minUnits: 1,
      maxUnits: 4,
      logPublishingOptions: {
        isLoggingEnabled: true,
        cloudWatchLogDestination: {
          logGroup: logGroupName,
        },
      },
      // vpcOptions: {
      //   subnetIds: subnetIdsPipeline,
      //   securityGroupIds: securityGroupIds,
      // },
      pipelineConfigurationBody: pipelineConfigYAML,
    })

    pipeline.node.addDependency(pipelineRole)
  }
}

export class ServerlessOpenSearch {
  public readonly searchDomain: string
  public readonly arn: string
  public readonly collection: {
    instance: opensearchserverless.CfnCollection
    grantSearchDocumentsAccess: (resources: iam.IGrantable[]) => void
    grantIndexDocumentsAccess: (resources: iam.IGrantable[]) => void
    grantManageIndexesAccess: (resources: iam.IGrantable[]) => void
    setupDynamodbIngestion: (tableArn: string) => void
  }

  constructor(scope: Construct, props: IProps) {
    const openSearchConstruct = new OpenSearchConstruct(scope, props)

    this.searchDomain = openSearchConstruct.searchDomain

    this.collection = {
      instance: openSearchConstruct.collection,
      grantSearchDocumentsAccess: (resources: iam.IGrantable[]) =>
        openSearchConstruct.grantAccess({
          permissionSlug: 's',
          collection: openSearchConstruct.collection,
          resources,
          indexPermissions: ['aoss:ReadDocument'],
        }),
      grantIndexDocumentsAccess: (resources: iam.IGrantable[]) =>
        openSearchConstruct.grantAccess({
          permissionSlug: 'i',
          collection: openSearchConstruct.collection,
          resources,
          indexPermissions: ['aoss:DescribeIndex', 'aoss:UpdateIndex', 'aoss:WriteDocument'],
        }),
      grantManageIndexesAccess: (resources: iam.IGrantable[]) =>
        openSearchConstruct.grantAccess({
          permissionSlug: 'm',
          collection: openSearchConstruct.collection,
          resources,
          indexPermissions: [
            'aoss:CreateIndex',
            'aoss:DescribeIndex',
            'aoss:UpdateIndex',
            'aoss:DeleteIndex',
          ],
        }),
      setupDynamodbIngestion: (tableArn: string) =>
        openSearchConstruct.setupDynamodbIngestion(tableArn),
    }
  }
}
