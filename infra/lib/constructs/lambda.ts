import {
  aws_cloudwatch as cloudwatch,
  Duration,
  aws_iam as iam,
  aws_lambda as lambda,
  aws_lambda_event_sources as lambdaEventSources,
  aws_sqs as sqs,
} from 'aws-cdk-lib'
import { Metric, MetricOptions } from 'aws-cdk-lib/aws-cloudwatch'
import { Construct } from 'constructs'
import { config } from '../../config'
import { AppStage, LambdaMetricActions } from '../enums'

interface ILambdaFunctionConstructProps {
  prefix: string
  functionName: string
  handler: string
  timeoutSecs: number
  memoryMB: number
  reservedConcurrentExecutions?: number
  provisionedConcurrentExecutions?: number
  sourceCodePath: string
  environment?: Record<string, string>
  deadLetterQueue?: sqs.IQueue
  layers?: lambda.ILayerVersion[]
  eventSources?: {
    queueSource?: {
      queue: sqs.IQueue
      props?: lambdaEventSources.SqsEventSourceProps
    }
  }[]
  rolePermissions?: iam.PolicyStatementProps[]
}

interface ISharedFunctionLayerConstructProps {
  prefix: string
  assetPath: string
}

export class SharedFunctionLayer {
  public readonly layer: lambda.ILayerVersion

  constructor(scope: Construct, props: ISharedFunctionLayerConstructProps) {
    this.layer = new lambda.LayerVersion(scope, `${props.prefix}-shared-function-layer`, {
      code: lambda.Code.fromAsset(props.assetPath),
      compatibleRuntimes: [lambda.Runtime.NODEJS_18_X],
      description: 'Contains node module dependencies for lambda functions',
    })
  }
}

export class LambdaFunction {
  public readonly lambdaFn: lambda.Function

  public readonly lambdaFnAlias: lambda.Alias

  constructor(scope: Construct, props: ILambdaFunctionConstructProps) {
    const role = new iam.Role(scope, `${props.prefix}-${props.functionName}-role`, {
      roleName: `${props.prefix}-${props.functionName}-role`,
      assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
    })

    role.addManagedPolicy(
      iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole'),
    )
    role.addManagedPolicy(iam.ManagedPolicy.fromAwsManagedPolicyName('AWSXrayWriteOnlyAccess'))

    if (config.validatedEnvs.STAGE === AppStage.LOCAL) {
      delete props.environment?.AWS_DEFAULT_REGION
    }

    for (const permission of props.rolePermissions || [])
      role.addToPolicy(new iam.PolicyStatement(permission))

    this.lambdaFn = new lambda.Function(scope, `${props.prefix}-${props.functionName}-fn`, {
      functionName: `${props.prefix}-${props.functionName}`,
      code: lambda.Code.fromAsset(props.sourceCodePath),
      handler: props.handler,
      runtime: lambda.Runtime.NODEJS_18_X,
      timeout: Duration.seconds(props.timeoutSecs),
      layers: props.layers,
      memorySize: props.memoryMB,
      reservedConcurrentExecutions: props.reservedConcurrentExecutions,
      tracing: lambda.Tracing.ACTIVE,
      role,
      environment: props.environment,
      deadLetterQueue: props.deadLetterQueue,
      description: `Deployed on ${new Date().toISOString()}`,
    })

    this.lambdaFnAlias = new lambda.Alias(
      scope,
      `${props.prefix}-${props.functionName}-current-alias`,
      {
        aliasName: 'Current',
        version: this.lambdaFn.currentVersion,
        provisionedConcurrentExecutions: props.provisionedConcurrentExecutions,
      },
    )

    for (const eventSource of props.eventSources || []) {
      if (eventSource.queueSource) {
        const sqsEventSource = new lambdaEventSources.SqsEventSource(
          eventSource.queueSource.queue,
          eventSource.queueSource.props,
        )
        this.sourceLambda.addEventSource(sqsEventSource)
      }
    }
  }

  get sourceLambda() {
    // Lmabda alias doesn't work in the local environment
    return config.validatedEnvs.STAGE === AppStage.LOCAL ? this.lambdaFn : this.lambdaFnAlias
  }

  getCustomMetricName = (action: LambdaMetricActions) =>
    `fn:${this.lambdaFn.functionName}.${action}`

  getMetric = (metricName: string, options?: MetricOptions) => {
    return this.lambdaFn.metric(metricName, options)
  }
}

interface IGroupedLambdaProps<T> {
  type:
    | 'Product'
    | 'Category'
    | 'Product Prices'
    | 'Auto Onboarding Processing'
    | 'Auto Onboarding API'
    | 'Enable Events'
    | 'Company Products'
  environment?: Record<string, string>
  functionProps: {
    [key in keyof T]: ILambdaFunctionConstructProps
  }
}

export class GroupedLambdaFunctions<T> {
  type: IGroupedLambdaProps<T>['type']

  functionMap: {
    [key in keyof IGroupedLambdaProps<T>['functionProps']]: LambdaFunction
  } = {} as any

  constructor(scope: Construct, props: IGroupedLambdaProps<T>) {
    this.type = props.type

    Object.entries(props.functionProps).forEach(([key, functionProp]) => {
      this.functionMap[key as keyof T] = new LambdaFunction(scope, {
        ...(functionProp as ILambdaFunctionConstructProps),
        environment: {
          ...props.environment,
          ...(functionProp as ILambdaFunctionConstructProps).environment,
        },
      })
    })
  }

  getMetrics = (metricName: string, options?: MetricOptions) => {
    return Object.entries(this.functionMap).reduce((metricArray: Metric[], [key, fn]) => {
      const fnMetric = (fn as LambdaFunction).lambdaFn.metric(metricName, {
        ...options,
        label: options?.label || key,
      })

      return [...metricArray, fnMetric]
    }, [])
  }

  get logGroupNames() {
    return Object.entries(this.functionMap).reduce((logGroupNames: string[], [key, fn]) => {
      const functionName = (fn as LambdaFunction).lambdaFn.functionName

      const logGroupName = `/aws/lambda/${functionName}`

      return [...logGroupNames, logGroupName]
    }, [])
  }

  get graphWidets(): cloudwatch.GraphWidgetProps[] {
    return [
      {
        title: `Lambda - ${this.type} Server Error Counts`,
        width: 4,
        height: 5,
        statistic: 'Sum',
        period: Duration.minutes(1),
        left: this.getMetrics('Errors'),
      },
      {
        title: `Lambda - ${this.type} Duration`,
        width: 4,
        height: 5,
        statistic: 'Maximum',
        period: Duration.minutes(1),
        left: this.getMetrics('Duration'),
      },
    ]
  }
}
