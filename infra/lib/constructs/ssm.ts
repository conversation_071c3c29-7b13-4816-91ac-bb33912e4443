import { Construct  } from 'constructs'
import { aws_ssm as ssm } from 'aws-cdk-lib'

interface ISSMParameterProps {
  prefix: string
  parameterName: string
}

export class GetStringListSSMParameter {
  public readonly parameter: ssm.IStringListParameter

  constructor(scope: Construct, props: ISSMParameterProps) {
    this.parameter = ssm.StringListParameter.fromStringListParameterName(
      scope,
      `${props.parameterName.replace(/\//g, '-')}-parameter`,
      props.parameterName
    )
  }
}

interface GetSystemParameterProps {
  prefix: string
  path: string
}

export class GetSystemParameter {
  public readonly value: string
  public readonly arn: string

  constructor(scope: Construct, props: GetSystemParameterProps) {
    const parameter = ssm.StringParameter.fromStringParameterAttributes(scope, `${props.prefix}-${props.path.replace(/\//g, '-')}-parameters`, {
      parameterName: props.path
    })

    this.value = parameter.stringValue
    this.arn = parameter.parameterArn
  }
}

