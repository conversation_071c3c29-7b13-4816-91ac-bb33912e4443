import { Construct } from 'constructs'
import {
  RemovalPolicy,
  aws_s3 as s3,
  aws_iam as iam,
  aws_sqs as sqs,
  aws_s3_notifications as s3Notifications,
} from 'aws-cdk-lib'
import { config } from '../../config'
import { stageValue, toPascalCase } from '../utils'

interface IS3BucketProps {
  name: string
  encryption?: boolean
  corsConfig?: s3.CorsRule[]
  readonlyExternalPrincipalArns?: string[]
  writeonlyExternalPrincipalArns?: string[]
  eventDestinations?: {
    s3Destination?: {
      eventType: s3.EventType
      queue: sqs.IQueue
    }
  }[]
}

export class S3Bucket {
  public readonly bucket: s3.Bucket
  public readonly apiGwProxyRole: iam.Role

  constructor(scope: Construct, props: IS3BucketProps) {
    const bucketName = `${config.projectName}-${props.name}-bucket`
    this.bucket = new s3.Bucket(scope, bucketName, {
      versioned: false,
      bucketName,
      eventBridgeEnabled: true,
      publicReadAccess: false,
      objectOwnership: s3.ObjectOwnership.BUCKET_OWNER_ENFORCED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      autoDeleteObjects: stageValue.bool({ production: false }, true),
      removalPolicy: stageValue.str(
        { production: RemovalPolicy.RETAIN },
        RemovalPolicy.DESTROY,
      ) as RemovalPolicy,
      encryption: props.encryption ? s3.BucketEncryption.S3_MANAGED : undefined,
      cors: props.corsConfig,
    })

    for (const eventDestination of props.eventDestinations || []) {
      if (eventDestination.s3Destination) {
        this.bucket.addEventNotification(
          eventDestination.s3Destination.eventType,
          new s3Notifications.SqsDestination(eventDestination.s3Destination.queue),
        )
      }
    }

    if (props.readonlyExternalPrincipalArns) {
      const allowExternalReadAccessPolicy = new iam.PolicyStatement({
        sid: `Allow${toPascalCase(props.name)}ReadAccessFromPartner`,
        effect: iam.Effect.ALLOW,
        resources: [this.bucket.bucketArn, this.bucket.arnForObjects('*')],
        actions: ['s3:GetObject'],
        principals: props.readonlyExternalPrincipalArns.map((arn) => new iam.ArnPrincipal(arn)),
      })
      this.bucket.addToResourcePolicy(allowExternalReadAccessPolicy)
    }

    if (props.writeonlyExternalPrincipalArns) {
      const allowExternalWriteAccessPolicy = new iam.PolicyStatement({
        sid: `Allow${toPascalCase(props.name)}WriteAccessFromPartner`,
        effect: iam.Effect.ALLOW,
        resources: [this.bucket.bucketArn, this.bucket.arnForObjects('*')],
        actions: ['s3:PutObject', 's3:PutObjectAcl'],
        principals: props.writeonlyExternalPrincipalArns.map((arn) => new iam.ArnPrincipal(arn)),
      })
      this.bucket.addToResourcePolicy(allowExternalWriteAccessPolicy)
    }
  }
}

export const S3EventType = s3.EventType
export const S3HTTPMethods = s3.HttpMethods
