import { aws_apigateway as apigw, aws_sqs as sqs, aws_iam as iam, Aws } from 'aws-cdk-lib'
import { Construct } from 'constructs'
import { AppStage, HttpMethods } from '../enums'
import { LambdaFunction } from './lambda'
import { config } from '../../config'
import { IParameterItem } from '../interfaces'
import { createRequestTemplate } from '../helpers'
import { CommonMetricOptions, Metric } from 'aws-cdk-lib/aws-cloudwatch'
import { getRootApiIdentifiers } from '../../constants/api'

interface IIntegration {
  method: HttpMethods
  handler: LambdaFunction
  authorizationType?: apigw.AuthorizationType
  apigwMethodOptions: apigw.MethodOptions
  lambdaIntegrationOption?: apigw.LambdaIntegrationOptions
}

interface IRoute {
  resourcePath: string[]
  integrations: IIntegration[]
}

interface INestedApiProps {
  baseResource: apigw.IResource
  requestAuthorizer?: apigw.IAuthorizer
  routes: IRoute[]
}

// This may need to be updated
export const defaultCorsPreflightOptions: apigw.CorsOptions = {
  allowOrigins: apigw.Cors.ALL_ORIGINS,
  allowMethods: apigw.Cors.ALL_METHODS,
  allowHeaders: [...apigw.Cors.DEFAULT_HEADERS, 'X-Idempotency-Key', 'X-Client-Id'],
}

export class NestedApiResources {
  public readonly restApi: apigw.IRestApi

  constructor(scope: Construct, props: INestedApiProps) {
    let resource = props.baseResource

    props.routes.forEach((route) => {
      route.resourcePath.forEach((path) => {
        resource = resource.addResource(path, {
          defaultCorsPreflightOptions,
        })
      })

      route.integrations.forEach((integration) => {
        const authorizationType = props.requestAuthorizer
          ? apigw.AuthorizationType.CUSTOM
          : apigw.AuthorizationType.NONE

        const handler = integration.handler.sourceLambda

        resource.addMethod(
          integration.method,
          new apigw.LambdaIntegration(handler, integration.lambdaIntegrationOption),
          {
            ...integration.apigwMethodOptions,
            authorizationType,
            authorizer: props.requestAuthorizer,
          },
        )
      })
    })
  }
}

interface Props {
  prefix: string
  baseResource: apigw.Resource
  integration: {
    queue: sqs.IQueue
    queueName: string
    method: HttpMethods
    resourcePaths: string[]
    integrationHttpMethod: HttpMethods
    pathParameters: IParameterItem[]
    queryParameters?: IParameterItem[]
    contextParameters?: IParameterItem[]
  }
}

/**
 * References:
 * https://blog.pocketgalaxy.io/posts/2021-09-19-api-gateway-sqs-integration.html
 * https://sbstjn.com/blog/aws-cdk-api-gateway-service-integration-sqs/
 */
export class ApigwSqsLambda {
  constructor(scope: Construct, props: Props) {
    let resource = props.baseResource

    const credentialsRole = new iam.Role(
      scope,
      `${props.prefix}-payment-service-sqs-role-${props.integration.queueName}`,
      {
        assumedBy: new iam.ServicePrincipal('apigateway.amazonaws.com'),
      },
    )

    const inlinePolicy = new iam.Policy(
      scope,
      `${props.prefix}-payment-service-sqs-policy-${props.integration.queueName}`,
      {
        statements: [
          new iam.PolicyStatement({
            actions: ['sqs:SendMessage'],
            effect: iam.Effect.ALLOW,
            resources: [props.integration.queue.queueArn],
          }),
        ],
      },
    )

    credentialsRole.attachInlinePolicy(inlinePolicy)

    props.integration.resourcePaths.forEach((path) => {
      resource = resource.addResource(path)
    })

    const integration = new apigw.AwsIntegration({
      service: 'sqs',
      path: `${Aws.ACCOUNT_ID}/${props.integration.queue.queueName}`,
      integrationHttpMethod: props.integration.integrationHttpMethod,
      options: {
        credentialsRole,
        passthroughBehavior: apigw.PassthroughBehavior.NEVER,
        requestParameters: {
          'integration.request.header.Content-Type': `'application/x-www-form-urlencoded'`,
        },
        requestTemplates: {
          'application/json': createRequestTemplate(
            props.integration.pathParameters,
            props.integration.queryParameters,
          ),
        },
        integrationResponses: [
          {
            statusCode: '200',
            responseTemplates: {
              'application/json': `{ message: '[accepted]' }`,
            },
          },
        ],
      },
    })

    resource.addMethod(props.integration.method, integration, {
      methodResponses: [{ statusCode: '200' }],
    })
  }
}

interface IExistingAPIGatewayRestApiProps {
  prefix: string
  serviceName?: string
  resources?: string[]
}

export const getRequestAuthorizer = (authorizerId: string) => {
  /**
   * authorizer lambda can be found here:
   * https://github.com/GetGreenline/blaze-infra-core/blob/main/api-gateway/lib/lambda-fns/src/handlers/authorize-request.ts
   */
  if (config.validatedEnvs.STAGE === AppStage.LOCAL) return
  return {
    authorizationType: apigw.AuthorizationType.CUSTOM,
    authorizerId: authorizerId,
  }
}

export class ExistingAPIGatewayRestApi {
  private prefix: string
  public restApi: apigw.IRestApi
  public baseResource: apigw.IResource

  constructor(scope: Construct, props: IExistingAPIGatewayRestApiProps) {
    this.prefix = props.prefix
    const restApiResourceId = `${props.prefix}-${config.validatedEnvs.STAGE}-rest-api`

    const rootApiIdentifiers = getRootApiIdentifiers()

    this.restApi =
      config.validatedEnvs.STAGE === AppStage.LOCAL
        ? new apigw.RestApi(scope, restApiResourceId)
        : apigw.RestApi.fromRestApiAttributes(scope, restApiResourceId, {
            restApiId: rootApiIdentifiers.restApiId,
            rootResourceId: rootApiIdentifiers.rootResourceId,
          })

    let baseResource = props.serviceName
      ? this.restApi.root.addResource(props.serviceName)
      : this.restApi.root

    const resources = props.resources || []
    resources.forEach((resource) => {
      baseResource = baseResource.addResource(resource, {
        defaultCorsPreflightOptions,
      })
    })

    this.baseResource = baseResource
  }

  setDeployment = (scope: Construct) => {
    // https://github.com/aws/aws-cdk/issues/13526#issuecomment-1011216177
    const deployment = new apigw.Deployment(
      scope,
      `${this.prefix}-${config.validatedEnvs.STAGE}-deployment`,
      {
        api: this.restApi,
      },
    )

    ;(deployment as any).resource.stageName = 'prod'
  }

  getMetric = (metricName: string, options?: CommonMetricOptions) => {
    let metric = new Metric({
      namespace: 'AWS/ApiGateway',
      metricName,
      dimensionsMap: {
        ApiName: 'BLAZE API',
      },
    })

    if (options) {
      metric = metric.with(options)
    }

    return metric
  }
}
