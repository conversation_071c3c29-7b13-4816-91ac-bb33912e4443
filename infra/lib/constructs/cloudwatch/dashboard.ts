import { Construct } from 'constructs'
import {
  aws_cloudwatch as cloudwatch
} from 'aws-cdk-lib'

interface IDashboardProps {
  prefix: string
  graphWidgetPropsArray: cloudwatch.GraphWidgetProps[]
  textWidgetPropsArray: cloudwatch.TextWidgetProps[]
  logQueryWidgetPropsArray: cloudwatch.LogQueryWidgetProps[]
}

const createGraphWidgets = (widgetPropsArray: cloudwatch.GraphWidgetProps[]) => {
  return widgetPropsArray.map((widgetProps: cloudwatch.GraphWidgetProps) => new cloudwatch.GraphWidget(widgetProps))
}

const createLogQueryWidgets = (widgetPropsArray: cloudwatch.LogQueryWidgetProps[]) => {
  return widgetPropsArray.map((widgetProps: cloudwatch.LogQueryWidgetProps) => new cloudwatch.LogQueryWidget(widgetProps))
}

const createTextWidgets = (widgetPropsArray: cloudwatch.TextWidgetProps[]) => {
  return widgetPropsArray.map((widgetProps: cloudwatch.TextWidgetProps) => new cloudwatch.TextWidget(widgetProps))
}

export class Dashboard {
  public readonly dashboard: cloudwatch.Dashboard

  constructor(scope: Construct, props: IDashboardProps) {
    this.dashboard = new cloudwatch.Dashboard(scope, `${props.prefix}-dashboard`, {
      dashboardName: `${props.prefix}-metrics`,
    })

    const widgets = [
      ...createGraphWidgets(props.graphWidgetPropsArray),
      ...createTextWidgets(props.textWidgetPropsArray),
      ...createLogQueryWidgets(props.logQueryWidgetPropsArray),
    ]

    this.dashboard.addWidgets(...widgets)
  }
}