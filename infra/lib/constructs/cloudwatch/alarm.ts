import {
  Stack,
  aws_cloudwatch as cloudwatch,
  aws_cloudwatch_actions as cw_actions,
  aws_sns as sns,
} from 'aws-cdk-lib'
import { Construct } from 'constructs'

export interface IAlarmConfig extends cloudwatch.CreateAlarmOptions {
  metric: cloudwatch.Metric | cloudwatch.MathExpression
}

interface ICustomTopicProps {
  topicName: string
  existing?: boolean
}
interface IAlarmMapperProps<T> {
  prefix: string

  topicProps: {
    [key in keyof T]: ICustomTopicProps & sns.TopicProps
  }
}

export class AlarmMapper<T> {
  topicMap: {
    [key in keyof IAlarmMapperProps<T>['topicProps']]: sns.ITopic
  } = {} as any

  private prefix

  constructor(scope: Construct, props: IAlarmMapperProps<T>) {
    this.prefix = props.prefix

    this.addTopics(scope, props.topicProps)
  }

  /**
   * Unforunately, there doesn't seem to be a way to add targets to existing slack channels.
   * Please manually add them in aws.
   */
  mapAlarmsToTopic = (scope: Construct, topicName: keyof T, alarmConfigs: IAlarmConfig[]) => {
    const topic = this.topicMap[topicName]
    const alarmAction = new cw_actions.SnsAction(topic)

    alarmConfigs.forEach((alarmConfig) => {
      const alarm = alarmConfig.metric.createAlarm(
        scope,
        `${this.prefix}-${alarmConfig.alarmName}`,
        alarmConfig,
      )

      alarm.addAlarmAction(alarmAction)
    })
  }

  addTopics = (scope: Construct, topicProps: IAlarmMapperProps<T>['topicProps']) => {
    Object.entries(topicProps).forEach(([key, topicProp]) => {
      const { existing, ...tp } = topicProp as ICustomTopicProps & sns.TopicProps

      const existingTopicArn = `arn:aws:sns:${Stack.of(scope).region}:${Stack.of(scope).account}:${
        tp.topicName
      }`

      const topic = existing
        ? sns.Topic.fromTopicArn(scope, `${tp.topicName}-topic`, existingTopicArn)
        : new sns.Topic(scope, `${tp.topicName}-topic`, tp)

      this.topicMap[key as keyof T] = topic
    })
  }
}
