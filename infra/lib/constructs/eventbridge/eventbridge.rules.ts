import { aws_events as events } from 'aws-cdk-lib'
import { EventSources, EventTopics, EventTypes } from '../../enums'
import { Construct } from 'constructs'
import { config } from '../../../config'
import { RuleProps } from 'aws-cdk-lib/aws-events'
import { CommonMetricOptions, Metric } from 'aws-cdk-lib/aws-cloudwatch'
import { CfnConnector } from 'aws-cdk-lib/aws-transfer'

export class EventRule {
  rule: events.Rule

  private ruleName: string

  private eventBusName: string

  constructor(scope: Construct, props: IEventRuleProps) {
    this.ruleName = props.ruleName

    this.eventBusName = props.eventBus? props.eventBus.eventBusName: ''

    this.rule = new events.Rule(scope, props.ruleName, props)
  }

  getMetric = (metricName: string, options?: CommonMetricOptions) => {
    let metric = new Metric({
      namespace: 'AWS/Events',
      metricName,
      dimensionsMap: {
        RuleName: this.ruleName,
        EventBusName: this.eventBusName,
      },
    })

    if (options) {
      metric = metric.with(options)
    }

    return metric
  }
}

interface IEventRuleProps extends RuleProps {
  eventBus?: events.IEventBus
  ruleName: string
}

interface IGroupedEventRuleProps<T> {
  ruleProps: {
    [key in keyof T]: IEventRuleProps
  }
}

export class GroupedEventRules<T> {
  ruleMap: {
    [key in keyof IGroupedEventRuleProps<T>['ruleProps']]: EventRule
  } = {} as any

  constructor(scope: Construct, props: IGroupedEventRuleProps<T>) {
    Object.entries(props.ruleProps).forEach(([key, ruleProp]) => {
      this.ruleMap[key as keyof T] = new EventRule(scope, ruleProp as IEventRuleProps)
    })
  }

  getMetrics = (metricName: string, options?: CommonMetricOptions) => {
    return Object.entries(this.ruleMap).reduce((metricArray: Metric[], [key, rule]) => {
      const ruleMetric = (rule as EventRule).getMetric(metricName, {
        ...options,
        label: options?.label || key,
      })

      return [...metricArray, ruleMetric]
    }, [])
  }
}

export const predefinedEventRuleProps = {
  catchAllLogRule: (eventBus: events.IEventBus): IEventRuleProps => {
    const ruleName = `${config.projectName}-catch-all-log-rule`

    return {
      eventBus,
      ruleName,
      description: 'Call all product-service event logs',
      enabled: true,
      eventPattern: {
        source: [EventSources.PRODUCT_SERVICE],
      },
    }
  },

  saleRule: (eventBus: events.IEventBus): IEventRuleProps => {
    const eventTopic = EventTopics.SALE
    const ruleName = `${config.projectName}-${eventTopic}-rule`

    return {
      eventBus,
      ruleName,
      description: `Process ${eventTopic} events`,
      enabled: true,
      eventPattern: {
        detailType: [`${eventTopic}.${EventTypes.COMPLETED}`]
      },
    }
  },
  merchantRule: (eventBus: events.IEventBus): IEventRuleProps => {
    const eventTopic = EventTopics.MERCHANT
    const ruleName = `${config.projectName}-${eventTopic}-rule`

    return {
      eventBus,
      ruleName,
      description: `Process ${eventTopic} events`,
      enabled: true,
      eventPattern: {
        detailType: [`${eventTopic}.${EventTypes.UPDATED}`]
      },
    }
  },

  transferFamilyRule: (eventBus: events.IEventBus, connector: CfnConnector): IEventRuleProps => {
    const ruleName = `${config.projectName}-transfer-family-rule`

    return {
      eventBus,
      ruleName,
      description: `Process transfer family events`,
      enabled: true,
      eventPattern: {
        source: ['aws.transfer'],
        detail: {
          'connector-id': [connector.attrConnectorId]
        }
      },
    }
  }
}
