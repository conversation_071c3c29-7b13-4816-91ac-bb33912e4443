import {
  RemovalPolicy,
  aws_events as events,
  aws_events_targets as eventsTargets,
  aws_logs as logs,
} from 'aws-cdk-lib'
import { Construct } from 'constructs'
import { config } from '../../../config'
import { EventRule } from './eventbridge.rules'
import { IQueue } from 'aws-cdk-lib/aws-sqs'
import { stageValue } from '../../utils'

interface EventsAppSyncApiDestinationProps {
  prefix: string
  ruleTargetMap: {
    rules: EventRule[]
    targets: events.IRuleTarget[]
  }[]
}

export const addTargetsToRules = (props: EventsAppSyncApiDestinationProps) => {
  props.ruleTargetMap.forEach((mapItem) => {
    mapItem.targets.forEach((target) => {
      mapItem.rules.forEach((rule) => {
        rule.rule.addTarget(target)
      })
    })
  })
}

export const predefinedEventTargets = {
  catchAllLogTarget: (scope: Construct, deadLetterQueue?: IQueue) => {
    const catchAllLogGroup = new logs.LogGroup(scope, `${config.projectName}-catch-all-log-group`, {
      logGroupName: `${config.projectName}-catch-all-log-group`,
      removalPolicy: stageValue.other(
        {
          production: RemovalPolicy.RETAIN,
          staging: RemovalPolicy.DESTROY,
          sandbox: RemovalPolicy.DESTROY,
        },
        RemovalPolicy.DESTROY,
      ),
    })

    return new eventsTargets.CloudWatchLogGroup(catchAllLogGroup, {
      deadLetterQueue,
    })
  },
  forSaleRule: (queue: IQueue, props?: eventsTargets.SqsQueueProps) => {
    return new eventsTargets.SqsQueue(queue, props)
  },
  forMerchantRule: (queue: IQueue, props?: eventsTargets.SqsQueueProps) => {
    return new eventsTargets.SqsQueue(queue, props)
  },
  forTransferFamilyRule: (queue: IQueue, props?: eventsTargets.SqsQueueProps) => {
    return new eventsTargets.SqsQueue(queue, props)
  }
}