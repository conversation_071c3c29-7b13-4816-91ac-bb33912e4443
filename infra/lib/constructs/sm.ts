import { Construct  } from 'constructs'
import { aws_secretsmanager as secrets } from 'aws-cdk-lib'

interface IGetSecretProps {
  prefix: string
  secretName: string
}

export class GetSecret {
  public readonly secret: secrets.ISecret

  constructor(scope: Construct, props: IGetSecretProps) {
    this.secret = secrets.Secret.fromSecretNameV2(
      scope,
      `${props.secretName.replace(/\//g, '-')}-secrets`,
      props.secretName
    )
  }
}

