import { Construct } from 'constructs'
import { aws_stepfunctions as sf, aws_lambda as lambda } from 'aws-cdk-lib'
import { config } from '../../../config'

interface ISFMapProps {
  name: string
  itemsPath: `$.${string}`
  maxConcurrency?: number
  resultPath?: string
  processorTask?: sf.IChainable
}

export class SFMap {
  public readonly map: sf.Map

  constructor(scope: Construct, props: ISFMapProps) {
    const mapName = `${config.projectName}-${props.name}`
    this.map = new sf.Map(scope, mapName, {
      itemsPath: props.itemsPath,
      maxConcurrency: props.maxConcurrency,
      resultPath: props.resultPath,
    })

    if (props.processorTask) this.map.itemProcessor(props.processorTask)
  }
}

export const ESFJsonPath = sf.JsonPath
