import { Construct } from 'constructs'
import {
  aws_stepfunctions as sf,
  aws_stepfunctions_tasks as sfTasks,
  aws_lambda as lambda,
} from 'aws-cdk-lib'
import { config } from '../../../config'

interface ISFLambdaTaskProps {
  name: string
  lambdaFn: lambda.IFunction
  payload?: Record<string, any>
  resultPath?: string
  outputPath?: string
}

export class SFLambdaTask {
  public readonly task: sfTasks.LambdaInvoke

  constructor(scope: Construct, props: ISFLambdaTaskProps) {
    const taskName = `${config.projectName}-${props.name}`
    this.task = new sfTasks.LambdaInvoke(scope, taskName, {
      lambdaFunction: props.lambdaFn,
      outputPath: props.outputPath,
      stateName: taskName,
      payload: props.payload ? sf.TaskInput.fromObject(props.payload) : undefined,
      resultPath: props.resultPath,
    })
  }
}

export const ESFJsonPath = sf.JsonPath
