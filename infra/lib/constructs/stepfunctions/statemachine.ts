import { Construct } from 'constructs'
import { aws_stepfunctions as sf, aws_lambda as lambda, Duration } from 'aws-cdk-lib'
import { config } from '../../../config'

interface ISFStateMachineProps {
  name: string
  definition: sf.IChainable
  timeout?: number
}

export class SFStateMachine {
  public readonly stateMachine: sf.StateMachine

  constructor(scope: Construct, props: ISFStateMachineProps) {
    const stateMachineName = `${config.projectName}-${props.name}`
    this.stateMachine = new sf.StateMachine(scope, stateMachineName, {
      stateMachineName,
      definition: props.definition,
      timeout: Duration.seconds(props.timeout ?? 900),
    })
  }
}

export const SFStateMachineState = {
  Succeed: sf.Succeed,
  Fail: sf.Fail,
  Wait: sf.Wait,
}
