import { Construct } from 'constructs'
import { aws_stepfunctions as sf, aws_lambda as lambda } from 'aws-cdk-lib'
import { ESFJsonPath, SFLambdaTask } from '../task'
import { EDomain } from '../../../enums'
import { SFMap } from '../map'

interface ISFDomainFlowProps {
  domain: EDomain
  ingestFn: lambda.IFunction
  importFn: lambda.IFunction
  iterateImport?: boolean
}

export class SFDomainFlow {
  public readonly chain: sf.IChainable

  constructor(scope: Construct, props: ISFDomainFlowProps) {
    // ingest file
    const { task: ingestTask } = new SFLambdaTask(scope, {
      name: `${props.domain}-ingest-task`,
      lambdaFn: props.ingestFn,
      payload: {
        domain: props.domain,
        'companyId.$': '$$.Execution.Input.companyId',
        'executionDomains.$': '$$.Execution.Input.executionDomains',
      },
      outputPath: '$.Payload',
    })

    // import rows (by‑shop or single)
    const { task: importTask } = new SFLambdaTask(scope, {
      name: `${props.domain}-import-task`,
      lambdaFn: props.importFn,
      payload: {
        domain: props.domain,
        'companyId.$': '$$.Execution.Input.companyId',
        'executionDomains.$': '$$.Execution.Input.executionDomains',
        'batch.$': props.iterateImport ? '$' : null,
      },
      resultPath: ESFJsonPath.DISCARD,
    })

    if (props.iterateImport) {
      const { map: batchesMap } = new SFMap(scope, {
        name: `${props.domain}-batches-map`,
        itemsPath: '$.batches',
        resultPath: ESFJsonPath.DISCARD,
        maxConcurrency: 1,
        processorTask: importTask,
      })
      this.chain = ingestTask.next(batchesMap)
    } else {
      this.chain = ingestTask.next(importTask)
    }
  }
}
