import { Construct } from 'constructs'
import { aws_stepfunctions as sf, aws_lambda as lambda } from 'aws-cdk-lib'
import { config } from '../../../../config'
import { EDomain } from '../../../enums'
import { SFDomainFlow } from './domain-flow'

interface ISFParallelJobsProps {
  phase: number
  ingestFn: lambda.IFunction
  importFn: lambda.IFunction
  jobs: {
    domain: EDomain
    iterateImport?: boolean
  }[]
}

export class SFParallelJobs {
  public readonly jobsGroup: sf.Parallel

  constructor(scope: Construct, props: ISFParallelJobsProps) {
    const parallelJobsGroupName = `${config.projectName}-phase-${props.phase}-parallel-jobs`

    const parallelGroup = new sf.Parallel(scope, parallelJobsGroupName)

    for (const job of props.jobs) {
      const { chain: domainChain } = new SFDomainFlow(scope, {
        domain: job.domain,
        ingestFn: props.ingestFn,
        importFn: props.importFn,
        iterateImport: job.iterateImport,
      })
      parallelGroup.branch(domainChain)
    }

    this.jobsGroup = parallelGroup
  }
}
