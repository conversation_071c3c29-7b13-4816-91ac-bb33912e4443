import { Construct } from 'constructs'
import { 
  <PERSON>ack, 
  StackProps, 
  aws_iam as iam,
  aws_transfer as transfer, 
} from 'aws-cdk-lib'
import { config } from '../../config'
import { GetSecret } from './sm'
import { LambdaFunction } from './lambda'

interface ITransferConnectorProps {
  bucketName: string
  stackTags: StackProps['tags']
}

/**
 * reference: https://docs.aws.amazon.com/transfer/latest/userguide/configure-sftp-connector.html
 */
export class TransferSetup {
  accessRole: iam.Role
  logRole: iam.Role
  connector: transfer.CfnConnector

  constructor(scope: Construct, props: ITransferConnectorProps) {
    this.accessRole = this.buildAccessRole(scope, props.bucketName)
    this.logRole = this.buildLogRole(scope)
    this.connector = this.buildConnector(scope, props.stackTags)
  }

  grantStartFileTransfer = (lambda: LambdaFunction) => {
    const policyStatement = new iam.PolicyStatement({
      actions: ['transfer:StartFileTransfer'],
      resources: [this.connector.attrArn]
    })
    lambda.lambdaFnAlias.role?.addToPrincipalPolicy(policyStatement)
  }

  private buildAccessRole = (scope: Construct, bucketName: string) => {
    const roleName = `${config.projectName}-pmp-connector-access-role`
    const role = this.createTransferRole(scope, roleName)

    role.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'secretsmanager:GetSecretValue',
      ],
      resources: [
        this.buildSecretArn(scope)
      ]
    }))

    role.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        's3:ListBucket',
        's3:GetBucketLocation'
      ],
      resources: [`arn:aws:s3:::${bucketName}`]
    }))

    role.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        's3:PutObject',
        's3:GetObject',
        's3:DeleteObject',
        's3:DeleteObjectVersion',
        's3:GetObjectVersion',
        's3:GetObjectACL',
        's3:PutObjectACL'
      ],
      resources: [`arn:aws:s3:::${bucketName}/*`]
    }))
    return role
  }

  private buildLogRole = (scope: Construct) => {
    const roleName = `${config.projectName}-pmp-connector-log-role`
    const role = this.createTransferRole(scope, roleName)

    role.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'logs:CreateLogStream',
        'logs:DescribeLogStreams',
        'logs:CreateLogGroup',
        'logs:PutLogEvents'
      ],
      resources: [
        'arn:aws:logs:*:*:log-group:/aws/transfer/*'
      ]
    }))
    return role
  }

  private createTransferRole = (scope: Construct, roleName: string) => {
    return new iam.Role(scope, roleName, {
      roleName,
      assumedBy: new iam.ServicePrincipal('transfer.amazonaws.com')
    })
  }

  private buildConnector = (scope: Construct, stackTags: ITransferConnectorProps['stackTags']) => {
    const connectorSecrets = new GetSecret(scope, {
      prefix: config.projectName,
      secretName: `${config.aws.secretsmanager.transferFamilySecretName}`,
    })

    const connectorName = `${config.projectName}-pmp-connector`

    return new transfer.CfnConnector(scope, connectorName, {
      url: this.getSecretValue(connectorSecrets, 'URL'),
      accessRole: this.accessRole.roleArn,
      loggingRole: this.logRole.roleArn,
      sftpConfig: {
        trustedHostKeys: [
          this.getSecretValue(connectorSecrets, 'TRUSTED_HOST_KEY')
        ],
        userSecretId: this.getSecretValue(connectorSecrets, 'SECRET_ARN')
      },
      tags: this.buildConnectorTags(connectorName, stackTags),
    })
  }

  private getSecretValue = (secret: GetSecret, key: string) => {
    return secret.secret.secretValueFromJson(key).unsafeUnwrap()
  }

  private buildConnectorTags = (connectorName: string, stackTags: StackProps['tags'] = {}) => {
    return Object.entries({ ...stackTags, Name: connectorName }).map(([key, value]) => ({ key, value }))
  }

  private buildSecretArn = (scope: Construct) => {
    return `arn:aws:secretsmanager:${Stack.of(scope).region}:${Stack.of(scope).account}:secret:${config.aws.secretsmanager.transferFamilySecretName}-*`
  }
}