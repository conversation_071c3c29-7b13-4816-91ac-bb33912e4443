import { Construct } from 'constructs'
import { aws_dynamodb as dynamodb, RemovalPolicy } from 'aws-cdk-lib'

interface Props {
  prefix: string
  tableName: string
  partitionKey: dynamodb.Attribute
  sortKey?: dynamodb.Attribute
  deletionProtection?: boolean
  globalSecondaryIndexes?: dynamodb.GlobalSecondaryIndexProps[]
  writeAccessPrincipalArn?: string
  stream?: dynamodb.StreamViewType
}

export class DynamoDB {
  table: dynamodb.Table
  static prefix = {
    gsi: 'gsi',
  }

  constructor(scope: Construct, props: Props) {
    const table = new dynamodb.Table(scope, `${props.prefix}-${props.tableName}`, {
      tableName: props.tableName,
      partitionKey: props.partitionKey,
      sortKey: props.sortKey,
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      deletionProtection: props.deletionProtection,
      pointInTimeRecovery: true,
      stream: props.stream || undefined,
      removalPolicy: props.deletionProtection ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
    })

    for (const gsi of props.globalSecondaryIndexes || []) {
      table.addGlobalSecondaryIndex(gsi)
    }

    this.table = table
  }
}

export const DDBAttributeType = dynamodb.AttributeType
