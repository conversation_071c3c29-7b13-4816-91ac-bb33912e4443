import { Construct } from 'constructs'
import { stageValue } from '../utils'
import { 
  Stack, 
  aws_iam as iam,
  aws_s3 as s3,
} from 'aws-cdk-lib'

interface Props {
  prefix: string
  bucket: s3.IBucket
}

export class SESMailManager {
  role: iam.Role

  BUCKET_PREFIX = 'forwarded-pmp-emails'

  constructor(scope: Construct, props: Props) {
    const roleName = `${props.prefix}-ses-mail-manager-pmp-role`
    const rulesetId = stageValue.str({
      sandbox: 'rs-yg24m3waweoduejzebrpdb57',
      staging: 'rs-e4xymwpzudyum4huv635zvw3',
      production: 'rs-yg24m3waweoduejzebrpdb57', // will need update
    }, 'rs-yg24m3waweoduejzebrpdb57')

    this.role = new iam.Role(scope, roleName, {
      roleName,
      assumedBy: new iam.ServicePrincipal('ses.amazonaws.com', {
        conditions: {
          ArnLike: {
            'AWS:SourceArn': `arn:aws:ses:${Stack.of(scope).region}:${Stack.of(scope).account}:mailmanager-rule-set/${rulesetId}`
          }
        }
      })
    })

    this.role.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        's3:PutObject',
      ],
      resources: [`arn:aws:s3:::${props.bucket.bucketName}/${this.BUCKET_PREFIX}/*`]
    }))
  }
}