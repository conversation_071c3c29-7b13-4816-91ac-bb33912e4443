import * as path from 'path'
import * as dotenv from 'dotenv'
import { buildSSORoleArn, getDeploymentStage, stageValue, validateEnv } from './lib/utils'
import { REQUIRED_ENV_VARIABLES } from './lib/types'

dotenv.config({
  path: path.resolve(__dirname, `./.env.${getDeploymentStage(process.env.STAGE)}`),
})

export const requiredEnvs: Array<keyof REQUIRED_ENV_VARIABLES> = [
  'CDK_DEFAULT_ACCOUNT',
  'AWS_DEFAULT_REGION',
  'DEPLOYED_BY',
  'STAGE',
]
const validatedEnvs = validateEnv(requiredEnvs, process.env)
const stage = getDeploymentStage(validatedEnvs.STAGE)

const projectId = 'products-service' as const
const projectName = `${stage}-${projectId}` as const
const smSecretsRoot = `${stage}/${projectId}` as const

const shortProjectId = projectId
  .split('-')
  .map(([firstChar]) => firstChar)
  .join('')
const projectAcronym = `${stage}-${shortProjectId}` as const

export const config = {
  projectName,
  projectAcronym,
  stage,
  stack: {
    env: {
      account: validatedEnvs.CDK_DEFAULT_ACCOUNT,
      region: validatedEnvs.AWS_DEFAULT_REGION,
    },
  },
  aws: {
    event: {
      eventBusName: `blaze-${stage}-platform-eventbus`,
      productRuleName: `${stage}-platform-gl-product-events-rule`,
    },
    sm: {
      blazeCanadaAPIKeySecretName: `${smSecretsRoot}/blaze-canada-api-key`,
    },
    ssm: {
      platformEventBusName: `/${smSecretsRoot}/platform-event-bus-name`,
      productRuleArn: `/${smSecretsRoot}/product-rule-arn`,
    },
    s3: {
      blazeCanadaBulkCSVBucketName: stageValue.str(
        {
          production: 'secure-temp-files',
          staging: 'greenline-staging-api-s3-securetemps3imagebucket-1qmgp5wb7zfvv',
        },
        'greenline-core-api-s3-1qz-securetemps3imagebucket-dqj59ezhocc3',
      ),
    },
    iam: {
      blazeCanadaECSTaskRoleARNs: stageValue.other(
        {
          production: [
            'arn:aws:iam::************:role/gl-prod-api-internal-wo-pvt-ecs-api-task-role',
            'arn:aws:iam::************:role/gl-prod-api-internal-wo-heavy-pvt-ecs-api-task-role',
          ],
          staging: [
            'arn:aws:iam::328972178557:role/gl-staging-api-internal-wo-pvt-ecs-api-task-role',
            'arn:aws:iam::328972178557:role/gl-staging-api-internal-wo-heavy-pvt-ecs-api-task-role',
          ],
        },
        [
          'arn:aws:iam::291565368193:role/gl-sandbox-api-internal-wo-pvt-ecs-api-task-role',
          'arn:aws:iam::291565368193:role/gl-sandbox-api-internal-wo-heavy-pvt-ecs-api-task-role',
        ],
      ),
      blazeUSECSTaskRoleARNs: stageValue.other(
        {
          production: [
            'arn:aws:iam::218419970704:role/prod-pvt-heavy-platform-task',
            'arn:aws:iam::218419970704:role/prod-pvt-main-platform-task',
          ],
          staging: [
            'arn:aws:iam::394147191967:role/staging-pvt-heavy-platform-task',
            'arn:aws:iam::394147191967:role/staging-pvt-main-platform-task',
          ],
        },
        [
          'arn:aws:iam::394147191967:role/staging-pvt-heavy-platform-task',
          'arn:aws:iam::394147191967:role/staging-pvt-main-platform-task',
        ],
      ),
    },
  },
  deployedBy: process.env.DEPLOYED_BY || 'Github',
  validatedEnvs,
  githubRepo: 'GetGreenline/product-service',
} as const

export const getOpenSearchSSORoleArns = (): string[] => {
  return stageValue.other<string[]>(
    {
      production: [buildSSORoleArn('AWSReservedSSO_DeveloperLeadAccess_5c68e0b052efbf1a')],
      staging: [
        buildSSORoleArn('AWSReservedSSO_PowerUserAccess_94534df95364b047'),
        buildSSORoleArn('AWSReservedSSO_DeveloperLeadAccess_b507e35284955eb5'),
      ],
      sandbox: [
        buildSSORoleArn('AWSReservedSSO_AdministratorAccess_cef8bfa4a24b9072'),
        buildSSORoleArn('AWSReservedSSO_PowerUserAccess_6702ff069b29d968'),
      ],
    },
    [],
  )
}
