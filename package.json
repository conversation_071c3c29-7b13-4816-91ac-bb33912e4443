{"name": "product-service", "scripts": {"prepare": "git config core.hooksPath .husky", "tsc:no-emit": "tsc -p src/tsconfig.json -noEmit", "repl": "ts-node src/repl.ts", "run:init": "cd local-setup && ./init.sh && cd ..", "build": "yarn tsc:no-emit && ./esbuild/build.mjs", "build:wac": "npx ghawac build && git add .github/workflows/*.yml", "test:app": "jest --config src/jest.config.js", "lint:lambda": "eslint src/**/*.ts --fix --cache", "lint:infra": "eslint infra/**/*.ts --fix --cache"}, "dependencies": {"@aws-lambda-powertools/logger": "^2.18.0", "@aws-lambda-powertools/metrics": "^2.18.0", "@aws-lambda-powertools/tracer": "^2.18.0", "@aws-sdk/client-eventbridge": "^3.637.0", "@aws-sdk/client-s3": "^3.614.0", "@aws-sdk/client-secrets-manager": "^3.787.0", "@aws-sdk/client-sfn": "^3.787.0", "@aws-sdk/client-sqs": "^3.624.0", "@aws-sdk/client-ssm": "^3.796.0", "@aws-sdk/client-transfer": "^3.624.0", "@aws-sdk/lib-storage": "^3.804.0", "@fast-csv/format": "^5.0.2", "@fast-csv/parse": "^5.0.2", "@getgreenline/products": "^3.30.28", "@kripod/uuidv7": "^0.3.4", "@middy/core": "^4.5.2", "@middy/http-cors": "^4.5.2", "@middy/http-error-handler": "^4.5.2", "@middy/sqs-partial-batch-failure": "^4.5.5", "@opensearch-project/opensearch": "^2.12.0", "@types/json-api-serializer": "^2.6.6", "aws-sdk": "^2.1642.0", "axios": "^1.7.2", "cheerio": "^1.0.0", "class-validator": "^0.14.1", "dayjs": "^1.11.11", "dotenv": "^16.4.5", "dynamoose": "^4.0.1", "http-errors": "^2.0.0", "js-yaml": "^4.1.0", "json-api-serializer": "^2.6.6", "lodash": "^4.17.21", "mailparser": "^3.7.1", "moment-timezone": "^0.5.45", "reflect-metadata": "^0.2.2", "ts-jsonapi": "^2.1.3", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@faker-js/faker": "^8.4.1", "@getgreenline/eslint-config-greenline": "^1.2.1", "@getgreenline/github-actions-wac": "^1.0.0", "@getgreenline/infra-utils": "^2.0.0", "@schedulino/aws-lambda-test-utils": "^1.2.0", "@types/aws-lambda": "^8.10.138", "@types/http-errors": "^2.0.4", "@types/jest": "^29.5.12", "@types/mailparser": "^3.4.4", "@types/node": "^20.14.2", "@typescript-eslint/parser": "^7.13.0", "babel-jest": "^29.7.0", "class-transformer": "^0.5.1", "concurrently": "^8.2.2", "esbuild": "^0.21.5", "esbuild-plugin-tsc": "^0.4.0", "eslint": "^9.4.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^9.0.11", "jest": "^29.7.0", "prettier": "^3.3.2", "ts-jest": "^29.1.4", "ts-node": "^10.9.2", "ts-node-repl": "^1.0.10", "typescript": "^5.4.5"}}