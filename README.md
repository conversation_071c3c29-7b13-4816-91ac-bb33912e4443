# Documentation

## Available git tags

| Keyword     | Description                                            |
| ----------- | ------------------------------------------------------ |
| `#runtests` | Will run tests in branches other than main and sandbox |

## Pre-requisites

- [aws-sam-cli](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html)
- [awslocal](https://github.com/localstack/awscli-local)
- [NoSQL Workbench](https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/workbench.settingup.html)
- [docker](https://www.docker.com/products/docker-desktop/)
  - **IMPORTANT**
    - AWS sam will not be able to detect the latest docker version if you're using M1 chip. Unfortunately, you MUST use an older version of Docker. Please read this [issue](https://github.com/aws/aws-sam-cli/issues/4329) for more info
    - Recommended Docker engine version is 20.10.14, and it comes with Docker version of **4.7.0**. Click [here](https://docs.docker.com/desktop/release-notes/#470) to download the version
- docker-compose version 2.x.x or higher

If docker doesn't work in Mac M1, you may use `colima` instead. Click the link to follow the instruction to set it up.

- [colima](https://earthly.dev/blog/homebrew-on-m1/)

## Setup

1. Download the repo

```bash
<NAME_EMAIL>:GetGreenline/product-service.git
```

2. Download nodemodules both at the root directory and in `/infra`

```bash
yarn install && cd infra && yarn install && cd ..
```

3. For M1 users, please run `colima` first. Then, run the initialize script (run containers, database scripts etc) to get the Payment Service up and running locally:

```bash
yarn run:init
```

4. Set up NoSQL Workbench

- Open NoSQL Workbench
- Go to Operation builder > + Add connection > DynamoDB local
- Add the following data
  - Connection name: local_dynamodb
  - Hostname: localhost
  - Port: 4566

![NoSQL Workbench Setup](docs/assets/nosql_workbench.png 'NoSQL Workbench Setup')

## Windows Setup

Note: Consider checking this section if the project is unable to run / build.

Pre-requisites:

1. WSL

WSL Configuration (Steps can be ommited if done previously)

1. Setup

- https://www.notion.so/greenlinepos/Setting-up-WSL-Windows-Sub-system-for-Linux-1ef07ef3cdfd40f89ae7b4a04a3ea3ee?pvs=4

2. SSH for github cli

- Also done in step 1
- Another reference: https://www.youtube.com/watch?v=X40b9x9BFGo&ab_channel=GeekForever

3. NPM login

- If npm login does not work, you could execute this instead: (replace YOUR_AUTH_TOKEN with access token generated in npm)

```bash
echo "//registry.npmjs.org/:_authToken=YOUR_AUTH_TOKEN" > ~/.npmrc
```

4. Install Node (using NVM)

- Install nvm

```bash
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.4/install.sh | bash
```

- Export nvm to session

```bash
  export NVM_DIR="$([ -z "${XDG_CONFIG_HOME-}" ] && printf %s "${HOME}/.nvm" || printf %s "${XDG_CONFIG_HOME}/nvm")"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
```

- Install Node

```bash
nvm install 20.15.1
```

- Verify Installation

```bash
node -v
npm -v
```

- Install typescript (if needed)

```bash
npm install -g typescript
```

- Install yarn (if needed)

```bash
npm install -g yarn
```

5. Update Ubuntu version

- Only check this step, when checking Node and has an error like "GLIBC_2.28 not found"
- One way would be updating manually the version:

```
Reference: https://stackoverflow.com/questions/72921215/getting-glibc-2-28-not-found (Copy-paste, in case the answer is deleted/modified, etc )
My requirements before upgrade:
Node.js (v18.x)
Windows 10
WSL 2 (distribution Ubuntu 18.04 (Bionic Beaver))
Making a backup from your WSL system (for your security)
If you don't use WSL you can skip the steps:

Open CMD or PowerShell as Admin.
Stop the WSL instance with wsl --shutdown
Create the folder wsl-backup with the command mkdir C:\wsl-backup\.
Run command wsl --export Ubuntu-18.04 C:\wsl-backup\ubuntu-18.04.tar.
If there is a problem in the process, you can restore your WSL using the command mkdir C:\WSL and wsl --import Ubuntu-18.04 C:\WSL C:\wsl-backup\ubuntu-18.04.tar to recover.

Upgrading Ubuntu 18.04 to 20.04
Removing the Snapd package:

For me it was necessary to remove the snapd package, because Ubuntu was not allowing me to upgrade to the 20.04 LTS version

sudo apt purge snapd
Run commands to update and upgrade packages:

sudo apt update
sudo apt list --upgradable
sudo apt upgrade -y
Install the update-manager-core package:

It’s essential to install this update manager core package this will trick the system into thinking there is a new LTS available and allow you to do an in-place upgrade.

sudo apt install update-manager-core
Install the new version with:

sudo do-release-upgrade
If you are using an LTS version (recommended) and found that there is no new LTS version available it will show this message

Checking for a new Ubuntu release
There is no development version of an LTS available.
To upgrade to the latest non-LTS development release
set Prompt=normal in /etc/update-manager/release-upgrades.
If it's not relevant to you to not use LTS versions, you can change the Prompt to normal in /etc/update-manager/release-upgrades file and rerun the previous command.

sudo do-release-upgrade -d
During installation, will need your interaction to allow installation of the additional packages that come in version 20.04 (including lib6 that is required by Node.js).

Finished, now can verify your distribution version Ubuntu using the command:

lsb_release -a
You should get an output similar to this

Distributor ID:    Ubuntu
Description:    Ubuntu 20.04.5 LTS
Release:    20.04
Codename:    focal
Now you can use Node.js version 18 or later

Install Node.js 18 with NVM
Installing Node.js LTS

In the current date (2022-11-26) is v18

nvm install lts
# Or nvm install --lts
Use the LTS version

nvm use lts
Check the Node.js version

node -v
# v18.12.1
```

- Other way that I found would be set the wsl distribution version:

```bash
  wsl --set-version Ubuntu-22.04 2
```

## How to locally start api

1. Run `yarn watch` at the root directory to watch for changes in `/src` and `/infra`
2. Open another terminal and run `yarn run:local` at the root directory

- This will enable both internal and external apigateway endpoints

3. Read the instruction on the terminal to make appropriate requests
